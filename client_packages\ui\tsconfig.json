{"compilerOptions": {"target": "ES2020", "useDefineForClassFields": true, "lib": ["ES2020", "DOM", "DOM.Iterable"], "module": "ESNext", "skipLibCheck": true, "moduleResolution": "bundler", "allowImportingTsExtensions": true, "resolveJsonModule": true, "isolatedModules": true, "noEmit": true, "jsx": "react-jsx", "strict": true, "noUnusedLocals": true, "noUnusedParameters": true, "noFallthroughCasesInSwitch": true, "baseUrl": "./src", "paths": {"@/*": ["./"], "@shared/*": ["../../shared/src/*"], "@components/*": ["./components/*"], "@pages/*": ["./pages/*"], "@store/*": ["./store/*"], "@hooks/*": ["./hooks/*"], "@utils/*": ["./utils/*"], "@styles/*": ["./styles/*"], "@types/*": ["./types/*"]}}, "include": ["src"], "references": [{"path": "./tsconfig.node.json"}]}
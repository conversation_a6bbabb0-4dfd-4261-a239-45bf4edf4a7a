export declare enum ServerToClientEvents {
    AUTH_RESPONSE = "auth:response",
    AUTH_LOGOUT = "auth:logout",
    CHARACTER_DATA = "character:data",
    CHARACTER_UPDATE = "character:update",
    CHARACTER_LIST = "character:list",
    SHOW_UI = "ui:show",
    HIDE_UI = "ui:hide",
    UPDATE_UI_DATA = "ui:updateData",
    SHOW_NOTIFICATION = "ui:showNotification",
    CHAT_MESSAGE = "chat:message",
    INVENTORY_UPDATE = "inventory:update",
    INVENTORY_OPEN = "inventory:open",
    INVENTORY_CLOSE = "inventory:close",
    VEHICLE_ENGINE_STATE = "vehicle:engineState",
    VEHICLE_LOCK_STATE = "vehicle:lockState",
    ADMIN_MESSAGE = "admin:message",
    ADMIN_TELEPORT = "admin:teleport",
    MONEY_UPDATE = "economy:moneyUpdate",
    BANK_UPDATE = "economy:bankUpdate",
    JOB_UPDATE = "job:update",
    JOB_DUTY_STATE = "job:dutyState"
}
export declare enum ClientToServerEvents {
    AUTH_LOGIN = "auth:login",
    AUTH_REGISTER = "auth:register",
    AUTH_LOGOUT = "auth:logout",
    CHARACTER_CREATE = "character:create",
    CHARACTER_SELECT = "character:select",
    CHARACTER_DELETE = "character:delete",
    CHARACTER_UPDATE_APPEARANCE = "character:updateAppearance",
    CHAT_SEND_MESSAGE = "chat:sendMessage",
    INVENTORY_USE_ITEM = "inventory:useItem",
    INVENTORY_DROP_ITEM = "inventory:dropItem",
    INVENTORY_MOVE_ITEM = "inventory:moveItem",
    INVENTORY_GIVE_ITEM = "inventory:giveItem",
    VEHICLE_ENGINE_TOGGLE = "vehicle:engineToggle",
    VEHICLE_LOCK_TOGGLE = "vehicle:lockToggle",
    VEHICLE_ENTER = "vehicle:enter",
    VEHICLE_EXIT = "vehicle:exit",
    ATM_WITHDRAW = "economy:atmWithdraw",
    ATM_DEPOSIT = "economy:atmDeposit",
    ATM_TRANSFER = "economy:atmTransfer",
    JOB_DUTY_TOGGLE = "job:dutyToggle",
    JOB_ACTION = "job:action",
    ADMIN_COMMAND = "admin:command",
    INTERACTION_USE = "interaction:use",
    INTERACTION_CANCEL = "interaction:cancel"
}
export declare enum UIToClientEvents {
    UI_LOGIN_SUBMIT = "ui:loginSubmit",
    UI_REGISTER_SUBMIT = "ui:registerSubmit",
    UI_CHARACTER_CREATE = "ui:characterCreate",
    UI_CHARACTER_SELECT = "ui:characterSelect",
    UI_CHARACTER_DELETE = "ui:characterDelete",
    UI_INVENTORY_USE_ITEM = "ui:inventoryUseItem",
    UI_INVENTORY_DROP_ITEM = "ui:inventoryDropItem",
    UI_INVENTORY_MOVE_ITEM = "ui:inventoryMoveItem",
    UI_CHAT_SEND = "ui:chatSend",
    UI_VEHICLE_ACTION = "ui:vehicleAction",
    UI_ATM_ACTION = "ui:atmAction",
    UI_JOB_ACTION = "ui:jobAction",
    UI_ADMIN_ACTION = "ui:adminAction",
    UI_CLOSE = "ui:close",
    UI_READY = "ui:ready"
}
export declare enum ClientToUIEvents {
    CLIENT_AUTH_STATE = "client:authState",
    CLIENT_CHARACTER_DATA = "client:characterData",
    CLIENT_CHARACTER_LIST = "client:characterList",
    CLIENT_INVENTORY_DATA = "client:inventoryData",
    CLIENT_CHAT_MESSAGE = "client:chatMessage",
    CLIENT_VEHICLE_DATA = "client:vehicleData",
    CLIENT_MONEY_DATA = "client:moneyData",
    CLIENT_JOB_DATA = "client:jobData",
    CLIENT_NOTIFICATION = "client:notification",
    CLIENT_UI_STATE = "client:uiState"
}
export interface IEventData {
    [key: string]: any;
}
export interface IAuthEventData {
    username?: string;
    password?: string;
    email?: string;
    token?: string;
}
export interface ICharacterEventData {
    characterId?: number;
    characterData?: any;
    appearanceData?: any;
}
export interface IChatEventData {
    type?: string;
    message?: string;
    target?: string;
}
export interface IInventoryEventData {
    itemId?: number;
    slot?: number;
    quantity?: number;
    targetSlot?: number;
    targetPlayerId?: number;
}
export interface IVehicleEventData {
    vehicleId?: number;
    action?: string;
    state?: boolean;
}
export interface IEconomyEventData {
    amount?: number;
    targetAccount?: string;
    targetPlayerId?: number;
    reason?: string;
}
export interface IJobEventData {
    jobId?: number;
    action?: string;
    data?: any;
}
export interface IAdminEventData {
    command?: string;
    targetId?: number;
    reason?: string;
    data?: any;
}
//# sourceMappingURL=index.d.ts.map
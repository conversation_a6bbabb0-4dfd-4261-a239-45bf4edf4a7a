import { createSlice, PayloadAction } from '@reduxjs/toolkit'

interface ChatMessage {
  id: string
  type: 'local' | 'global' | 'whisper' | 'shout' | 'me' | 'do' | 'ooc' | 'admin' | 'faction' | 'job'
  sender: string
  message: string
  timestamp: number
  range?: number
  color?: string
}

interface ChatState {
  messages: ChatMessage[]
  isVisible: boolean
  isInputOpen: boolean
  inputValue: string
  selectedType: string
  maxMessages: number
  filters: string[]
}

const initialState: ChatState = {
  messages: [],
  isVisible: true,
  isInputOpen: false,
  inputValue: '',
  selectedType: 'local',
  maxMessages: 100,
  filters: [],
}

const chatSlice = createSlice({
  name: 'chat',
  initialState,
  reducers: {
    addMessage: (state, action: PayloadAction<Omit<ChatMessage, 'id'>>) => {
      const message: ChatMessage = {
        ...action.payload,
        id: `msg_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      }
      
      state.messages.push(message)
      
      // Remove old messages if exceeding max
      if (state.messages.length > state.maxMessages) {
        state.messages = state.messages.slice(-state.maxMessages)
      }
    },
    clearMessages: (state) => {
      state.messages = []
    },
    toggleVisibility: (state) => {
      state.isVisible = !state.isVisible
    },
    setVisibility: (state, action: PayloadAction<boolean>) => {
      state.isVisible = action.payload
    },
    openInput: (state) => {
      state.isInputOpen = true
    },
    closeInput: (state) => {
      state.isInputOpen = false
      state.inputValue = ''
    },
    setInputValue: (state, action: PayloadAction<string>) => {
      state.inputValue = action.payload
    },
    setSelectedType: (state, action: PayloadAction<string>) => {
      state.selectedType = action.payload
    },
    setMaxMessages: (state, action: PayloadAction<number>) => {
      state.maxMessages = Math.max(10, Math.min(500, action.payload))
      
      // Trim messages if needed
      if (state.messages.length > state.maxMessages) {
        state.messages = state.messages.slice(-state.maxMessages)
      }
    },
    addFilter: (state, action: PayloadAction<string>) => {
      if (!state.filters.includes(action.payload)) {
        state.filters.push(action.payload)
      }
    },
    removeFilter: (state, action: PayloadAction<string>) => {
      state.filters = state.filters.filter(filter => filter !== action.payload)
    },
    clearFilters: (state) => {
      state.filters = []
    },
  },
})

export const {
  addMessage,
  clearMessages,
  toggleVisibility,
  setVisibility,
  openInput,
  closeInput,
  setInputValue,
  setSelectedType,
  setMaxMessages,
  addFilter,
  removeFilter,
  clearFilters,
} = chatSlice.actions

export default chatSlice.reducer

/**
 * User Model for MongoDB
 */

import mongoose, { Schema, Document } from 'mongoose';
import { AdminLevel } from '@ragemp-rp/shared';

export interface IUser extends Document {
  username: string;
  email: string;
  password: string;
  socialClub: string;
  adminLevel: AdminLevel;
  registrationDate: Date;
  lastLogin: Date;
  registrationIP: string;
  lastIP: string;
  banned: boolean;
  banReason?: string;
  banExpiry?: Date;
  bannedBy?: string;
  playtime: number;
  money: number;
  bankMoney: number;
  settings: {
    language: string;
    notifications: boolean;
    chatTimestamps: boolean;
    uiScale: number;
  };
  statistics: {
    totalLogins: number;
    charactersCreated: number;
    lastCharacterId?: mongoose.Types.ObjectId;
  };
  createdAt: Date;
  updatedAt: Date;
}

const UserSchema: Schema = new Schema({
  username: {
    type: String,
    required: true,
    unique: true,
    lowercase: true,
    trim: true,
    minlength: 3,
    maxlength: 20,
    match: /^[a-zA-Z0-9_]+$/
  },
  email: {
    type: String,
    required: true,
    unique: true,
    lowercase: true,
    trim: true,
    match: /^[^\s@]+@[^\s@]+\.[^\s@]+$/
  },
  password: {
    type: String,
    required: true,
    select: false // Don't include password in queries by default
  },
  socialClub: {
    type: String,
    required: true,
    trim: true
  },
  adminLevel: {
    type: Number,
    enum: Object.values(AdminLevel),
    default: AdminLevel.PLAYER
  },
  registrationDate: {
    type: Date,
    default: Date.now
  },
  lastLogin: {
    type: Date,
    default: Date.now
  },
  registrationIP: {
    type: String,
    required: true
  },
  lastIP: {
    type: String,
    required: true
  },
  banned: {
    type: Boolean,
    default: false
  },
  banReason: {
    type: String,
    default: null
  },
  banExpiry: {
    type: Date,
    default: null
  },
  bannedBy: {
    type: String,
    default: null
  },
  playtime: {
    type: Number,
    default: 0 // in minutes
  },
  money: {
    type: Number,
    default: 5000 // Starting money
  },
  bankMoney: {
    type: Number,
    default: 0
  },
  settings: {
    language: {
      type: String,
      default: 'en'
    },
    notifications: {
      type: Boolean,
      default: true
    },
    chatTimestamps: {
      type: Boolean,
      default: true
    },
    uiScale: {
      type: Number,
      default: 1.0,
      min: 0.5,
      max: 2.0
    }
  },
  statistics: {
    totalLogins: {
      type: Number,
      default: 0
    },
    charactersCreated: {
      type: Number,
      default: 0
    },
    lastCharacterId: {
      type: Schema.Types.ObjectId,
      ref: 'Character',
      default: null
    }
  }
}, {
  timestamps: true,
  collection: 'users'
});

// Indexes for better performance
UserSchema.index({ username: 1 });
UserSchema.index({ email: 1 });
UserSchema.index({ socialClub: 1 });
UserSchema.index({ banned: 1 });
UserSchema.index({ adminLevel: 1 });

// Pre-save middleware to increment login count
UserSchema.pre('save', function(next) {
  if (this.isModified('lastLogin')) {
    (this as any).statistics.totalLogins += 1;
  }
  next();
});

// Instance methods
UserSchema.methods.isBanned = function(): boolean {
  if (!this.banned) return false;
  if (!this.banExpiry) return true; // Permanent ban
  return new Date() < this.banExpiry;
};

UserSchema.methods.isAdmin = function(): boolean {
  return this.adminLevel > AdminLevel.PLAYER;
};

UserSchema.methods.canUseAdminCommand = function(requiredLevel: AdminLevel): boolean {
  return this.adminLevel >= requiredLevel;
};

UserSchema.methods.addPlaytime = function(minutes: number): void {
  this.playtime += minutes;
};

UserSchema.methods.getFormattedPlaytime = function(): string {
  const hours = Math.floor(this.playtime / 60);
  const minutes = this.playtime % 60;
  return `${hours}h ${minutes}m`;
};

// Static methods
UserSchema.statics.findByUsername = function(username: string) {
  return this.findOne({ username: username.toLowerCase() });
};

UserSchema.statics.findByEmail = function(email: string) {
  return this.findOne({ email: email.toLowerCase() });
};

UserSchema.statics.findBySocialClub = function(socialClub: string) {
  return this.findOne({ socialClub: socialClub });
};

UserSchema.statics.getBannedUsers = function() {
  return this.find({ banned: true });
};

UserSchema.statics.getAdmins = function() {
  return this.find({ adminLevel: { $gt: AdminLevel.PLAYER } });
};

export const UserModel = mongoose.model<IUser>('User', UserSchema);

import { useSelector } from 'react-redux'
import { motion } from 'framer-motion'
import { DollarSign, CreditCard } from 'lucide-react'
import { RootState } from '@/store'

const MoneyDisplay = () => {
  const { activeCharacter } = useSelector((state: RootState) => state.character)
  const { showMoney } = useSelector((state: RootState) => state.hud)

  if (!showMoney || !activeCharacter) return null

  const formatMoney = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount)
  }

  return (
    <motion.div
      initial={{ opacity: 0, x: 50 }}
      animate={{ opacity: 1, x: 0 }}
      className="space-y-2"
    >
      {/* Cash */}
      <motion.div
        initial={{ opacity: 0, y: -10 }}
        animate={{ opacity: 1, y: 0 }}
        className="glass-card p-3 min-w-[180px]"
      >
        <div className="flex items-center space-x-3">
          <DollarSign className="w-5 h-5 text-green-400" />
          <div className="flex-1">
            <div className="text-gray-300 text-xs">Cash</div>
            <div className="text-white font-bold text-lg">
              {formatMoney(activeCharacter.money || 0)}
            </div>
          </div>
        </div>
      </motion.div>

      {/* Bank */}
      <motion.div
        initial={{ opacity: 0, y: -10 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.1 }}
        className="glass-card p-3 min-w-[180px]"
      >
        <div className="flex items-center space-x-3">
          <CreditCard className="w-5 h-5 text-blue-400" />
          <div className="flex-1">
            <div className="text-gray-300 text-xs">Bank</div>
            <div className="text-white font-bold text-lg">
              {formatMoney(activeCharacter.bankMoney || 0)}
            </div>
          </div>
        </div>
      </motion.div>
    </motion.div>
  )
}

export default MoneyDisplay

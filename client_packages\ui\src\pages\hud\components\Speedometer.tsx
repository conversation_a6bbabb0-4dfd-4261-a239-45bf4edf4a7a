import { useState, useEffect } from 'react'
import { useSelector } from 'react-redux'
import { motion, AnimatePresence } from 'framer-motion'
import { Gauge, Fuel, Settings } from 'lucide-react'
import { RootState } from '@/store'

const Speedometer = () => {
  const { showSpeedometer } = useSelector((state: RootState) => state.hud)
  const [vehicleData] = useState({
    speed: 0,
    rpm: 0,
    fuel: 100,
    gear: 'P',
    isInVehicle: false,
    engineOn: false
  })

  useEffect(() => {
    // In a real implementation, this would get vehicle data from RageMP
    // For now, we'll simulate it
    const updateVehicleData = () => {
      if (window.mp) {
        // This would be replaced with actual vehicle data from RageMP
        // window.mp.trigger('getVehicleData')
      }
    }

    const interval = setInterval(updateVehicleData, 100)
    return () => clearInterval(interval)
  }, [])

  if (!showSpeedometer || !vehicleData.isInVehicle) return null

  const speedPercentage = Math.min((vehicleData.speed / 200) * 100, 100)
  const rpmPercentage = Math.min((vehicleData.rpm / 8000) * 100, 100)

  return (
    <AnimatePresence>
      <motion.div
        initial={{ opacity: 0, scale: 0.8, y: 50 }}
        animate={{ opacity: 1, scale: 1, y: 0 }}
        exit={{ opacity: 0, scale: 0.8, y: 50 }}
        className="glass-card p-4"
      >
        <div className="flex items-center space-x-4">
          {/* Speed Gauge */}
          <div className="relative">
            <div className="speedometer">
              <svg className="w-24 h-24" viewBox="0 0 100 100">
                {/* Background Circle */}
                <circle
                  cx="50"
                  cy="50"
                  r="45"
                  fill="none"
                  stroke="rgba(255,255,255,0.2)"
                  strokeWidth="2"
                />
                
                {/* Speed Arc */}
                <circle
                  cx="50"
                  cy="50"
                  r="45"
                  fill="none"
                  stroke="url(#speedGradient)"
                  strokeWidth="4"
                  strokeLinecap="round"
                  strokeDasharray={`${speedPercentage * 2.83} 283`}
                  transform="rotate(-90 50 50)"
                  className="transition-all duration-300"
                />
                
                {/* Gradient Definition */}
                <defs>
                  <linearGradient id="speedGradient" x1="0%" y1="0%" x2="100%" y2="0%">
                    <stop offset="0%" stopColor="#22c55e" />
                    <stop offset="50%" stopColor="#eab308" />
                    <stop offset="100%" stopColor="#ef4444" />
                  </linearGradient>
                </defs>
              </svg>
              
              {/* Speed Text */}
              <div className="absolute inset-0 flex flex-col items-center justify-center">
                <div className="text-white font-bold text-lg">
                  {Math.round(vehicleData.speed)}
                </div>
                <div className="text-gray-300 text-xs">
                  KM/H
                </div>
              </div>
            </div>
          </div>

          {/* Vehicle Info */}
          <div className="space-y-2">
            {/* Gear */}
            <div className="flex items-center space-x-2">
              <Settings className="w-4 h-4 text-gray-400" />
              <span className="text-white font-bold text-xl">
                {vehicleData.gear}
              </span>
            </div>

            {/* RPM */}
            <div className="space-y-1">
              <div className="flex items-center space-x-2">
                <Gauge className="w-4 h-4 text-blue-400" />
                <span className="text-white text-sm">
                  {Math.round(vehicleData.rpm)} RPM
                </span>
              </div>
              <div className="w-20 h-1 bg-white/20 rounded-full overflow-hidden">
                <div 
                  className="h-full bg-gradient-to-r from-blue-400 to-red-400 transition-all duration-300"
                  style={{ width: `${rpmPercentage}%` }}
                />
              </div>
            </div>

            {/* Fuel */}
            <div className="space-y-1">
              <div className="flex items-center space-x-2">
                <Fuel className="w-4 h-4 text-yellow-400" />
                <span className="text-white text-sm">
                  {Math.round(vehicleData.fuel)}%
                </span>
              </div>
              <div className="w-20 h-1 bg-white/20 rounded-full overflow-hidden">
                <div 
                  className="h-full bg-gradient-to-r from-red-400 to-green-400 transition-all duration-300"
                  style={{ width: `${vehicleData.fuel}%` }}
                />
              </div>
            </div>

            {/* Engine Status */}
            <div className="flex items-center space-x-2">
              <div 
                className={`w-2 h-2 rounded-full ${
                  vehicleData.engineOn ? 'bg-green-400' : 'bg-red-400'
                }`}
              />
              <span className="text-white text-xs">
                {vehicleData.engineOn ? 'Engine On' : 'Engine Off'}
              </span>
            </div>
          </div>
        </div>
      </motion.div>
    </AnimatePresence>
  )
}

export default Speedometer

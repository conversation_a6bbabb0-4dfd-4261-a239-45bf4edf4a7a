/**
 * Client-side Logger utility
 */

export enum LogLevel {
  DEBUG = 0,
  INFO = 1,
  WARN = 2,
  ERROR = 3,
  SUCCESS = 4
}

export class Logger {
  private static logLevel: LogLevel = LogLevel.INFO;

  /**
   * Set the minimum log level
   */
  public static setLogLevel(level: LogLevel): void {
    this.logLevel = level;
  }

  /**
   * Get current timestamp
   */
  private static getTimestamp(): string {
    const now = new Date();
    return now.toISOString().replace('T', ' ').substring(0, 19);
  }

  /**
   * Format log message
   */
  private static formatMessage(level: string, message: string, ...args: any[]): string {
    const timestamp = this.getTimestamp();
    const formattedArgs = args.length > 0 ? ' ' + args.map(arg => 
      typeof arg === 'object' ? JSON.stringify(arg, null, 2) : String(arg)
    ).join(' ') : '';
    
    return `[${timestamp}] [CLIENT] [${level}] ${message}${formattedArgs}`;
  }

  /**
   * Log debug message
   */
  public static debug(message: string, ...args: any[]): void {
    if (this.logLevel <= LogLevel.DEBUG) {
      const formatted = this.formatMessage('DEBUG', message, ...args);
      console.log(formatted);
    }
  }

  /**
   * Log info message
   */
  public static info(message: string, ...args: any[]): void {
    if (this.logLevel <= LogLevel.INFO) {
      const formatted = this.formatMessage('INFO', message, ...args);
      console.log(formatted);
    }
  }

  /**
   * Log warning message
   */
  public static warn(message: string, ...args: any[]): void {
    if (this.logLevel <= LogLevel.WARN) {
      const formatted = this.formatMessage('WARN', message, ...args);
      console.warn(formatted);
    }
  }

  /**
   * Log error message
   */
  public static error(message: string, ...args: any[]): void {
    if (this.logLevel <= LogLevel.ERROR) {
      const formatted = this.formatMessage('ERROR', message, ...args);
      console.error(formatted);
    }
  }

  /**
   * Log success message
   */
  public static success(message: string, ...args: any[]): void {
    if (this.logLevel <= LogLevel.SUCCESS) {
      const formatted = this.formatMessage('SUCCESS', message, ...args);
      console.log(formatted);
    }
  }
}

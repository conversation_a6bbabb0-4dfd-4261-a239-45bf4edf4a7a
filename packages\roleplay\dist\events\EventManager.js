"use strict";
/**
 * Event Manager for handling RageMP events
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.EventManager = void 0;
const Logger_1 = require("../utils/Logger");
const shared_1 = require("@ragemp-rp/shared");
class EventManager {
    static instance;
    eventHandlers = new Map();
    constructor() { }
    /**
     * Get singleton instance
     */
    static getInstance() {
        if (!EventManager.instance) {
            EventManager.instance = new EventManager();
        }
        return EventManager.instance;
    }
    /**
     * Initialize event manager
     */
    initialize() {
        Logger_1.Logger.info('Initializing Event Manager...');
        this.setupEventHandlers();
    }
    /**
     * Setup default event handlers
     */
    setupEventHandlers() {
        // Add default RageMP event handlers here if needed
        Logger_1.Logger.debug('Event handlers setup complete');
    }
    /**
     * Register event handler
     */
    on(eventName, handler) {
        if (!this.eventHandlers.has(eventName)) {
            this.eventHandlers.set(eventName, []);
        }
        this.eventHandlers.get(eventName).push(handler);
        // Register with RageMP if it's a client-to-server event
        if (Object.values(shared_1.ClientToServerEvents).includes(eventName)) {
            mp.events.add(eventName, handler);
        }
        Logger_1.Logger.debug(`Registered handler for event: ${eventName}`);
    }
    /**
     * Unregister event handler
     */
    off(eventName, handler) {
        if (!this.eventHandlers.has(eventName)) {
            return;
        }
        if (handler) {
            const handlers = this.eventHandlers.get(eventName);
            const index = handlers.indexOf(handler);
            if (index > -1) {
                handlers.splice(index, 1);
                mp.events.remove(eventName, handler);
            }
        }
        else {
            // Remove all handlers for this event
            const handlers = this.eventHandlers.get(eventName);
            handlers.forEach(h => mp.events.remove(eventName, h));
            this.eventHandlers.delete(eventName);
        }
        Logger_1.Logger.debug(`Unregistered handler(s) for event: ${eventName}`);
    }
    /**
     * Emit event to client(s)
     */
    emitClient(player, eventName, ...args) {
        try {
            if (Array.isArray(player)) {
                player.forEach(p => {
                    if (p && p.call) {
                        p.call(eventName, ...args);
                    }
                });
            }
            else {
                if (player && player.call) {
                    player.call(eventName, ...args);
                }
            }
            Logger_1.Logger.debug(`Emitted event ${eventName} to client(s)`);
        }
        catch (error) {
            Logger_1.Logger.error(`Failed to emit event ${eventName}:`, error);
        }
    }
    /**
     * Emit event to all clients
     */
    emitAllClients(eventName, ...args) {
        try {
            mp.players.call(eventName, ...args);
            Logger_1.Logger.debug(`Emitted event ${eventName} to all clients`);
        }
        catch (error) {
            Logger_1.Logger.error(`Failed to emit event ${eventName} to all clients:`, error);
        }
    }
    /**
     * Emit event to clients in range
     */
    emitClientsInRange(position, range, dimension, eventName, ...args) {
        try {
            const playersInRange = mp.players.toArray().filter(player => {
                if (player.dimension !== dimension)
                    return false;
                const distance = player.position.distanceTo(position);
                return distance <= range;
            });
            this.emitClient(playersInRange, eventName, ...args);
            Logger_1.Logger.debug(`Emitted event ${eventName} to ${playersInRange.length} clients in range`);
        }
        catch (error) {
            Logger_1.Logger.error(`Failed to emit event ${eventName} to clients in range:`, error);
        }
    }
    /**
     * Emit event to browser (CEF)
     */
    emitBrowser(player, eventName, data) {
        try {
            if (player && player.call) {
                player.call('cef:emit', [eventName, data]);
            }
            Logger_1.Logger.debug(`Emitted browser event ${eventName} to player ${player.name}`);
        }
        catch (error) {
            Logger_1.Logger.error(`Failed to emit browser event ${eventName}:`, error);
        }
    }
    /**
     * Call remote procedure on client
     */
    async callClient(player, eventName, ...args) {
        return new Promise((resolve, reject) => {
            try {
                const timeout = setTimeout(() => {
                    reject(new Error(`RPC call ${eventName} timed out`));
                }, 10000); // 10 second timeout
                const callId = this.generateCallId();
                const responseEvent = `rpc:response:${callId}`;
                // Setup one-time response handler
                const responseHandler = (responsePlayer, result, error) => {
                    if (responsePlayer === player) {
                        clearTimeout(timeout);
                        mp.events.remove(responseEvent, responseHandler);
                        if (error) {
                            reject(new Error(error));
                        }
                        else {
                            resolve(result);
                        }
                    }
                };
                mp.events.add(responseEvent, responseHandler);
                player.call('rpc:call', [eventName, callId, ...args]);
            }
            catch (error) {
                reject(error);
            }
        });
    }
    /**
     * Generate unique call ID for RPC
     */
    generateCallId() {
        return `${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    }
    /**
     * Get registered handlers for event
     */
    getHandlers(eventName) {
        return this.eventHandlers.get(eventName) || [];
    }
    /**
     * Check if event has handlers
     */
    hasHandlers(eventName) {
        return this.eventHandlers.has(eventName) && this.eventHandlers.get(eventName).length > 0;
    }
    /**
     * Get all registered events
     */
    getRegisteredEvents() {
        return Array.from(this.eventHandlers.keys());
    }
}
exports.EventManager = EventManager;
//# sourceMappingURL=EventManager.js.map
import { useState } from 'react'
import { 
  Card, 
  CardBody, 
  Input, 
  Button, 
  Select, 
  SelectItem,
  <PERSON>lider,
  Tabs,
  Tab
} from '@nextui-org/react'
import { Save, ArrowLeft, User, Palette, Shirt } from 'lucide-react'

const CharacterCreator = () => {
  const [characterData, setCharacterData] = useState({
    firstName: '',
    lastName: '',
    age: 18,
    gender: 'male',
    appearance: {
      model: 0,
      eyeColor: 0,
      hairColor: 0,
      hairStyle: 0,
      faceFeatures: Array(20).fill(0),
      headOverlays: Array(13).fill({ index: 0, opacity: 1.0, colorId: 0 })
    }
  })

  const [errors, setErrors] = useState<Record<string, string>>({})

  const validateForm = () => {
    const newErrors: Record<string, string> = {}
    
    if (!characterData.firstName.trim()) {
      newErrors.firstName = 'First name is required'
    } else if (characterData.firstName.length < 2) {
      newErrors.firstName = 'First name must be at least 2 characters'
    }
    
    if (!characterData.lastName.trim()) {
      newErrors.lastName = 'Last name is required'
    } else if (characterData.lastName.length < 2) {
      newErrors.lastName = 'Last name must be at least 2 characters'
    }
    
    if (characterData.age < 18 || characterData.age > 80) {
      newErrors.age = 'Age must be between 18 and 80'
    }
    
    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const handleSubmit = () => {
    if (!validateForm()) return
    
    if (window.mp) {
      window.mp.trigger('ui:characterCreate', characterData)
    }
  }

  const handleBack = () => {
    if (window.mp) {
      window.mp.trigger('ui:characterBack')
    }
  }

  const handleInputChange = (field: string, value: any) => {
    setCharacterData(prev => ({ ...prev, [field]: value }))
    
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }))
    }
  }

  const handleAppearanceChange = (field: string, value: any) => {
    setCharacterData(prev => ({
      ...prev,
      appearance: { ...prev.appearance, [field]: value }
    }))
  }

  return (
    <div className="fixed inset-0 flex p-4">
      {/* Left Panel - Character Preview */}
      <div className="w-1/2 pr-2">
        <Card className="glass-card-dark h-full">
          <CardBody className="p-6">
            <div className="character-preview">
              <div className="text-center text-white">
                <h3 className="text-xl font-bold mb-2">Character Preview</h3>
                <p className="text-gray-300 text-sm">
                  {characterData.firstName || 'First'} {characterData.lastName || 'Last'}
                </p>
                <p className="text-gray-400 text-xs">
                  {characterData.age} years old • {characterData.gender}
                </p>
              </div>
              
              {/* 3D Character preview would go here */}
              <div className="mt-8 h-96 bg-gradient-to-br from-gray-800 to-gray-900 rounded-lg flex items-center justify-center">
                <p className="text-gray-400">3D Character Preview</p>
              </div>
            </div>
          </CardBody>
        </Card>
      </div>

      {/* Right Panel - Customization */}
      <div className="w-1/2 pl-2">
        <Card className="glass-card-dark h-full">
          <CardBody className="p-6">
            <div className="flex items-center justify-between mb-6">
              <h2 className="text-2xl font-bold text-white">Create Character</h2>
              <Button
                className="btn-glass"
                size="sm"
                onPress={handleBack}
              >
                <ArrowLeft className="w-4 h-4 mr-2" />
                Back
              </Button>
            </div>

            <Tabs 
              aria-label="Character customization tabs"
              classNames={{
                tabList: "glass-card",
                cursor: "bg-primary-500/30",
                tab: "text-white",
                tabContent: "text-white"
              }}
            >
              {/* Basic Info Tab */}
              <Tab 
                key="basic" 
                title={
                  <div className="flex items-center space-x-2">
                    <User className="w-4 h-4" />
                    <span>Basic</span>
                  </div>
                }
              >
                <div className="space-y-4 mt-4">
                  <Input
                    label="First Name"
                    placeholder="Enter first name"
                    value={characterData.firstName}
                    onChange={(e) => handleInputChange('firstName', e.target.value)}
                    isInvalid={!!errors.firstName}
                    errorMessage={errors.firstName}
                    classNames={{
                      input: "text-white",
                      inputWrapper: "input-glass"
                    }}
                  />

                  <Input
                    label="Last Name"
                    placeholder="Enter last name"
                    value={characterData.lastName}
                    onChange={(e) => handleInputChange('lastName', e.target.value)}
                    isInvalid={!!errors.lastName}
                    errorMessage={errors.lastName}
                    classNames={{
                      input: "text-white",
                      inputWrapper: "input-glass"
                    }}
                  />

                  <div className="space-y-2">
                    <label className="text-white text-sm font-medium">Age: {characterData.age}</label>
                    <Slider
                      size="sm"
                      step={1}
                      minValue={18}
                      maxValue={80}
                      value={characterData.age}
                      onChange={(value) => handleInputChange('age', value)}
                      className="max-w-md"
                      classNames={{
                        track: "bg-white/20",
                        filler: "bg-primary-500"
                      }}
                    />
                  </div>

                  <Select
                    label="Gender"
                    placeholder="Select gender"
                    selectedKeys={[characterData.gender]}
                    onChange={(e) => handleInputChange('gender', e.target.value)}
                    classNames={{
                      trigger: "input-glass",
                      value: "text-white"
                    }}
                  >
                    <SelectItem key="male" value="male">Male</SelectItem>
                    <SelectItem key="female" value="female">Female</SelectItem>
                  </Select>
                </div>
              </Tab>

              {/* Appearance Tab */}
              <Tab 
                key="appearance" 
                title={
                  <div className="flex items-center space-x-2">
                    <Palette className="w-4 h-4" />
                    <span>Appearance</span>
                  </div>
                }
              >
                <div className="space-y-4 mt-4">
                  <div className="space-y-2">
                    <label className="text-white text-sm font-medium">Hair Style: {characterData.appearance.hairStyle}</label>
                    <Slider
                      size="sm"
                      step={1}
                      minValue={0}
                      maxValue={50}
                      value={characterData.appearance.hairStyle}
                      onChange={(value) => handleAppearanceChange('hairStyle', value)}
                      className="max-w-md"
                      classNames={{
                        track: "bg-white/20",
                        filler: "bg-primary-500"
                      }}
                    />
                  </div>

                  <div className="space-y-2">
                    <label className="text-white text-sm font-medium">Hair Color: {characterData.appearance.hairColor}</label>
                    <Slider
                      size="sm"
                      step={1}
                      minValue={0}
                      maxValue={63}
                      value={characterData.appearance.hairColor}
                      onChange={(value) => handleAppearanceChange('hairColor', value)}
                      className="max-w-md"
                      classNames={{
                        track: "bg-white/20",
                        filler: "bg-primary-500"
                      }}
                    />
                  </div>

                  <div className="space-y-2">
                    <label className="text-white text-sm font-medium">Eye Color: {characterData.appearance.eyeColor}</label>
                    <Slider
                      size="sm"
                      step={1}
                      minValue={0}
                      maxValue={31}
                      value={characterData.appearance.eyeColor}
                      onChange={(value) => handleAppearanceChange('eyeColor', value)}
                      className="max-w-md"
                      classNames={{
                        track: "bg-white/20",
                        filler: "bg-primary-500"
                      }}
                    />
                  </div>
                </div>
              </Tab>

              {/* Clothing Tab */}
              <Tab 
                key="clothing" 
                title={
                  <div className="flex items-center space-x-2">
                    <Shirt className="w-4 h-4" />
                    <span>Clothing</span>
                  </div>
                }
              >
                <div className="space-y-4 mt-4">
                  <p className="text-gray-300 text-sm">
                    Clothing customization will be available here
                  </p>
                </div>
              </Tab>
            </Tabs>

            {/* Create Button */}
            <div className="mt-8">
              <Button
                className="w-full btn-glass bg-green-500/20 border-green-400 hover:bg-green-500/30"
                size="lg"
                onPress={handleSubmit}
              >
                <Save className="w-4 h-4 mr-2" />
                Create Character
              </Button>
            </div>
          </CardBody>
        </Card>
      </div>
    </div>
  )
}

export default CharacterCreator

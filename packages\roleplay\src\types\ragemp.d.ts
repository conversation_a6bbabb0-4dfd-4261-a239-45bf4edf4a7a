/**
 * RageMP Server Type Definitions
 */

declare global {
  // Vector3 interface
  interface Vector3Mp {
    x: number;
    y: number;
    z: number;
    distanceTo(position: Vector3Mp): number;
  }

  // Player interface
  interface PlayerMp {
    id: number;
    name: string;
    socialClub: string;
    ip: string;
    ping: number;
    dimension: number;
    position: Vector3Mp;
    heading: number;
    health: number;
    armour: number;
    weapon: number;
    alpha: number;
    vehicle: VehicleMp | null;
    call(eventName: string, ...args: any[]): void;
    getWeaponAmmo(weapon: number): number;
  }

  // Vehicle interface
  interface VehicleMp {
    id: number;
    model: string;
    position: Vector3Mp;
    rotation: Vector3Mp;
    dimension: number;
    numberPlate: string;
    locked: boolean;
    engine: boolean;
    getSpeed(): number;
    getCurrentRpm(): number;
    getCurrentGear(): number;
    getIsEngineRunning(): boolean;
  }

  // Events interface
  interface EventsMp {
    add(eventName: string, handler: Function): void;
    remove(eventName: string, handler?: Function): void;
    call(eventName: string, ...args: any[]): void;
    callRemote(eventName: string, ...args: any[]): void;
  }

  // Players pool interface
  interface PlayersPool {
    length: number;
    toArray(): PlayerMp[];
    call(eventName: string, ...args: any[]): void;
    forEach(callback: (player: PlayerMp) => void): void;
  }

  // Vehicles pool interface
  interface VehiclesPool {
    length: number;
    toArray(): VehicleMp[];
    new(model: string, position: Vector3Mp, options?: any): VehicleMp;
  }

  // Main MP interface
  interface Mp {
    events: EventsMp;
    players: PlayersPool;
    vehicles: VehiclesPool;
    Vector3: new (x: number, y: number, z: number) => Vector3Mp;
  }

  // Global mp object
  var mp: Mp;
}

export {};

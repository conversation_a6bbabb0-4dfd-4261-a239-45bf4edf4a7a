import { useSelector } from 'react-redux'
import { motion, AnimatePresence } from 'framer-motion'
import { RootState } from '@/store'
import HealthBar from './components/HealthBar'
import MoneyDisplay from './components/MoneyDisplay'
import TimeDisplay from './components/TimeDisplay'
import LocationDisplay from './components/LocationDisplay'
import Speedometer from './components/Speedometer'

const HUD = () => {
  const { isVisible, scale, opacity } = useSelector((state: RootState) => state.hud)
  const { activeCharacter } = useSelector((state: RootState) => state.character)

  if (!isVisible || !activeCharacter) return null

  return (
    <AnimatePresence>
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: opacity }}
        exit={{ opacity: 0 }}
        className="fixed inset-0 pointer-events-none z-10"
        style={{ transform: `scale(${scale})` }}
      >
        {/* Top Left - Health/Stats */}
        <div className="absolute top-4 left-4 space-y-2 pointer-events-auto">
          <HealthBar />
        </div>

        {/* Top Right - Money/Time */}
        <div className="absolute top-4 right-4 space-y-2 pointer-events-auto">
          <MoneyDisplay />
          <TimeDisplay />
        </div>

        {/* Bottom Left - Location */}
        <div className="absolute bottom-4 left-4 pointer-events-auto">
          <LocationDisplay />
        </div>

        {/* Bottom Right - Speedometer (when in vehicle) */}
        <div className="absolute bottom-4 right-4 pointer-events-auto">
          <Speedometer />
        </div>

        {/* Chat Area - Bottom Left (above location) */}
        <div className="absolute bottom-20 left-4 w-96 h-64 pointer-events-auto">
          {/* Chat component will be added here */}
        </div>
      </motion.div>
    </AnimatePresence>
  )
}

export default HUD

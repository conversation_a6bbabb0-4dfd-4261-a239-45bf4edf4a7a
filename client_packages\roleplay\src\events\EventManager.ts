/**
 * Client-side Event Manager for handling RageMP events
 */

export class EventManager {
  private static instance: EventManager;
  private eventHandlers: Map<string, Function[]> = new Map();

  private constructor() {}

  /**
   * Get singleton instance
   */
  public static getInstance(): EventManager {
    if (!EventManager.instance) {
      EventManager.instance = new EventManager();
    }
    return EventManager.instance;
  }

  /**
   * Initialize event manager
   */
  public async initialize(): Promise<void> {
    console.log('🎯 Initializing Client Event Manager...');
    this.setupDefaultEvents();
  }

  /**
   * Setup default RageMP events
   */
  private setupDefaultEvents(): void {
    // Server to client events
    mp.events.add('server:message', (message: string) => {
      console.log('Server message:', message);
    });

    // CEF events
    mp.events.add('cef:ready', () => {
      console.log('CEF is ready');
    });

    console.log('✅ Default events setup complete');
  }

  /**
   * Register event handler
   */
  public on(eventName: string, handler: Function): void {
    if (!this.eventHandlers.has(eventName)) {
      this.eventHandlers.set(eventName, []);
    }
    
    this.eventHandlers.get(eventName)!.push(handler);
    mp.events.add(eventName, handler);
    
    console.log(`📡 Registered handler for event: ${eventName}`);
  }

  /**
   * Unregister event handler
   */
  public off(eventName: string, handler?: Function): void {
    if (!this.eventHandlers.has(eventName)) {
      return;
    }

    if (handler) {
      const handlers = this.eventHandlers.get(eventName)!;
      const index = handlers.indexOf(handler);
      if (index > -1) {
        handlers.splice(index, 1);
        mp.events.remove(eventName, handler);
      }
    } else {
      // Remove all handlers for this event
      const handlers = this.eventHandlers.get(eventName)!;
      handlers.forEach(h => mp.events.remove(eventName, h));
      this.eventHandlers.delete(eventName);
    }
  }

  /**
   * Call server event
   */
  public callServer(eventName: string, ...args: any[]): void {
    mp.events.callRemote(eventName, ...args);
  }

  /**
   * Call browser event
   */
  public callBrowser(browser: BrowserMp, eventName: string, ...args: any[]): void {
    if (browser && browser.execute) {
      browser.execute(`
        if (window.mp && window.mp.trigger) {
          window.mp.trigger('${eventName}', ${JSON.stringify(args)});
        }
      `);
    }
  }

  /**
   * Shutdown event manager
   */
  public async shutdown(): Promise<void> {
    console.log('🛑 Shutting down Client Event Manager...');
    
    // Remove all event handlers
    for (const [eventName, handlers] of this.eventHandlers) {
      handlers.forEach(handler => mp.events.remove(eventName, handler));
    }
    
    this.eventHandlers.clear();
  }
}

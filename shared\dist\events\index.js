"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ClientToUIEvents = exports.UIToClientEvents = exports.ClientToServerEvents = exports.ServerToClientEvents = void 0;
// Server to Client Events
var ServerToClientEvents;
(function (ServerToClientEvents) {
    // Authentication
    ServerToClientEvents["AUTH_RESPONSE"] = "auth:response";
    ServerToClientEvents["AUTH_LOGOUT"] = "auth:logout";
    // Character
    ServerToClientEvents["CHARACTER_DATA"] = "character:data";
    ServerToClientEvents["CHARACTER_UPDATE"] = "character:update";
    ServerToClientEvents["CHARACTER_LIST"] = "character:list";
    // UI
    ServerToClientEvents["SHOW_UI"] = "ui:show";
    ServerToClientEvents["HIDE_UI"] = "ui:hide";
    ServerToClientEvents["UPDATE_UI_DATA"] = "ui:updateData";
    ServerToClientEvents["SHOW_NOTIFICATION"] = "ui:showNotification";
    // Chat
    ServerToClientEvents["CHAT_MESSAGE"] = "chat:message";
    // Inventory
    ServerToClientEvents["INVENTORY_UPDATE"] = "inventory:update";
    ServerToClientEvents["INVENTORY_OPEN"] = "inventory:open";
    ServerToClientEvents["INVENTORY_CLOSE"] = "inventory:close";
    // Vehicle
    ServerToClientEvents["VEHICLE_ENGINE_STATE"] = "vehicle:engineState";
    ServerToClientEvents["VEHICLE_LOCK_STATE"] = "vehicle:lockState";
    // Admin
    ServerToClientEvents["ADMIN_MESSAGE"] = "admin:message";
    ServerToClientEvents["ADMIN_TELEPORT"] = "admin:teleport";
    // Economy
    ServerToClientEvents["MONEY_UPDATE"] = "economy:moneyUpdate";
    ServerToClientEvents["BANK_UPDATE"] = "economy:bankUpdate";
    // Job
    ServerToClientEvents["JOB_UPDATE"] = "job:update";
    ServerToClientEvents["JOB_DUTY_STATE"] = "job:dutyState";
})(ServerToClientEvents || (exports.ServerToClientEvents = ServerToClientEvents = {}));
// Client to Server Events
var ClientToServerEvents;
(function (ClientToServerEvents) {
    // Authentication
    ClientToServerEvents["AUTH_LOGIN"] = "auth:login";
    ClientToServerEvents["AUTH_REGISTER"] = "auth:register";
    ClientToServerEvents["AUTH_LOGOUT"] = "auth:logout";
    // Character
    ClientToServerEvents["CHARACTER_CREATE"] = "character:create";
    ClientToServerEvents["CHARACTER_SELECT"] = "character:select";
    ClientToServerEvents["CHARACTER_DELETE"] = "character:delete";
    ClientToServerEvents["CHARACTER_UPDATE_APPEARANCE"] = "character:updateAppearance";
    // Chat
    ClientToServerEvents["CHAT_SEND_MESSAGE"] = "chat:sendMessage";
    // Inventory
    ClientToServerEvents["INVENTORY_USE_ITEM"] = "inventory:useItem";
    ClientToServerEvents["INVENTORY_DROP_ITEM"] = "inventory:dropItem";
    ClientToServerEvents["INVENTORY_MOVE_ITEM"] = "inventory:moveItem";
    ClientToServerEvents["INVENTORY_GIVE_ITEM"] = "inventory:giveItem";
    // Vehicle
    ClientToServerEvents["VEHICLE_ENGINE_TOGGLE"] = "vehicle:engineToggle";
    ClientToServerEvents["VEHICLE_LOCK_TOGGLE"] = "vehicle:lockToggle";
    ClientToServerEvents["VEHICLE_ENTER"] = "vehicle:enter";
    ClientToServerEvents["VEHICLE_EXIT"] = "vehicle:exit";
    // Economy
    ClientToServerEvents["ATM_WITHDRAW"] = "economy:atmWithdraw";
    ClientToServerEvents["ATM_DEPOSIT"] = "economy:atmDeposit";
    ClientToServerEvents["ATM_TRANSFER"] = "economy:atmTransfer";
    // Job
    ClientToServerEvents["JOB_DUTY_TOGGLE"] = "job:dutyToggle";
    ClientToServerEvents["JOB_ACTION"] = "job:action";
    // Admin
    ClientToServerEvents["ADMIN_COMMAND"] = "admin:command";
    // Interaction
    ClientToServerEvents["INTERACTION_USE"] = "interaction:use";
    ClientToServerEvents["INTERACTION_CANCEL"] = "interaction:cancel";
})(ClientToServerEvents || (exports.ClientToServerEvents = ClientToServerEvents = {}));
// UI to Client Events
var UIToClientEvents;
(function (UIToClientEvents) {
    // Authentication
    UIToClientEvents["UI_LOGIN_SUBMIT"] = "ui:loginSubmit";
    UIToClientEvents["UI_REGISTER_SUBMIT"] = "ui:registerSubmit";
    // Character
    UIToClientEvents["UI_CHARACTER_CREATE"] = "ui:characterCreate";
    UIToClientEvents["UI_CHARACTER_SELECT"] = "ui:characterSelect";
    UIToClientEvents["UI_CHARACTER_DELETE"] = "ui:characterDelete";
    // Inventory
    UIToClientEvents["UI_INVENTORY_USE_ITEM"] = "ui:inventoryUseItem";
    UIToClientEvents["UI_INVENTORY_DROP_ITEM"] = "ui:inventoryDropItem";
    UIToClientEvents["UI_INVENTORY_MOVE_ITEM"] = "ui:inventoryMoveItem";
    // Chat
    UIToClientEvents["UI_CHAT_SEND"] = "ui:chatSend";
    // Vehicle
    UIToClientEvents["UI_VEHICLE_ACTION"] = "ui:vehicleAction";
    // Economy
    UIToClientEvents["UI_ATM_ACTION"] = "ui:atmAction";
    // Job
    UIToClientEvents["UI_JOB_ACTION"] = "ui:jobAction";
    // Admin
    UIToClientEvents["UI_ADMIN_ACTION"] = "ui:adminAction";
    // General
    UIToClientEvents["UI_CLOSE"] = "ui:close";
    UIToClientEvents["UI_READY"] = "ui:ready";
})(UIToClientEvents || (exports.UIToClientEvents = UIToClientEvents = {}));
// Client to UI Events
var ClientToUIEvents;
(function (ClientToUIEvents) {
    // Authentication
    ClientToUIEvents["CLIENT_AUTH_STATE"] = "client:authState";
    // Character
    ClientToUIEvents["CLIENT_CHARACTER_DATA"] = "client:characterData";
    ClientToUIEvents["CLIENT_CHARACTER_LIST"] = "client:characterList";
    // Inventory
    ClientToUIEvents["CLIENT_INVENTORY_DATA"] = "client:inventoryData";
    // Chat
    ClientToUIEvents["CLIENT_CHAT_MESSAGE"] = "client:chatMessage";
    // Vehicle
    ClientToUIEvents["CLIENT_VEHICLE_DATA"] = "client:vehicleData";
    // Economy
    ClientToUIEvents["CLIENT_MONEY_DATA"] = "client:moneyData";
    // Job
    ClientToUIEvents["CLIENT_JOB_DATA"] = "client:jobData";
    // Notifications
    ClientToUIEvents["CLIENT_NOTIFICATION"] = "client:notification";
    // General
    ClientToUIEvents["CLIENT_UI_STATE"] = "client:uiState";
})(ClientToUIEvents || (exports.ClientToUIEvents = ClientToUIEvents = {}));
//# sourceMappingURL=index.js.map
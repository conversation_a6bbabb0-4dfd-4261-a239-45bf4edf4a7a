"use strict";
/**
 * Database Manager for MongoDB connection and operations
 */
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.DatabaseManager = void 0;
const mongoose_1 = __importDefault(require("mongoose"));
const Logger_1 = require("../utils/Logger");
class DatabaseManager {
    static instance;
    isConnected = false;
    constructor() { }
    /**
     * Get singleton instance
     */
    static getInstance() {
        if (!DatabaseManager.instance) {
            DatabaseManager.instance = new DatabaseManager();
        }
        return DatabaseManager.instance;
    }
    /**
     * Connect to MongoDB database
     */
    async connect() {
        try {
            const mongoUri = process.env.MONGODB_URI || 'mongodb://localhost:27017/ragemp_roleplay';
            await mongoose_1.default.connect(mongoUri, {
                maxPoolSize: 10,
                serverSelectionTimeoutMS: 5000,
                socketTimeoutMS: 45000,
            });
            this.isConnected = true;
            Logger_1.Logger.success(`Connected to MongoDB: ${mongoUri}`);
            // Setup connection event handlers
            mongoose_1.default.connection.on('error', (error) => {
                Logger_1.Logger.error('MongoDB connection error:', error);
            });
            mongoose_1.default.connection.on('disconnected', () => {
                Logger_1.Logger.warn('MongoDB disconnected');
                this.isConnected = false;
            });
            mongoose_1.default.connection.on('reconnected', () => {
                Logger_1.Logger.success('MongoDB reconnected');
                this.isConnected = true;
            });
        }
        catch (error) {
            Logger_1.Logger.error('Failed to connect to MongoDB:', error);
            throw error;
        }
    }
    /**
     * Disconnect from database
     */
    async disconnect() {
        try {
            await mongoose_1.default.disconnect();
            this.isConnected = false;
            Logger_1.Logger.info('Disconnected from MongoDB');
        }
        catch (error) {
            Logger_1.Logger.error('Error disconnecting from MongoDB:', error);
            throw error;
        }
    }
    /**
     * Check if database is connected
     */
    get connected() {
        return this.isConnected && mongoose_1.default.connection.readyState === 1;
    }
    /**
     * Get database connection
     */
    getConnection() {
        return mongoose_1.default.connection;
    }
    /**
     * Execute database operation with error handling
     */
    async executeOperation(operation) {
        try {
            if (!this.connected) {
                throw new Error('Database not connected');
            }
            return await operation();
        }
        catch (error) {
            Logger_1.Logger.error('Database operation failed:', error);
            throw error;
        }
    }
    /**
     * Start database transaction
     */
    async startTransaction() {
        const session = await mongoose_1.default.startSession();
        session.startTransaction();
        return session;
    }
    /**
     * Commit transaction
     */
    async commitTransaction(session) {
        await session.commitTransaction();
        session.endSession();
    }
    /**
     * Abort transaction
     */
    async abortTransaction(session) {
        await session.abortTransaction();
        session.endSession();
    }
}
exports.DatabaseManager = DatabaseManager;
//# sourceMappingURL=DatabaseManager.js.map
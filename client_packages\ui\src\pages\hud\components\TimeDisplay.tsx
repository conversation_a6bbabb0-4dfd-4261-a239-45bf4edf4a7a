import { useState, useEffect } from 'react'
import { useSelector } from 'react-redux'
import { motion } from 'framer-motion'
import { Clock, Calendar } from 'lucide-react'
import { RootState } from '@/store'

const TimeDisplay = () => {
  const { showTime } = useSelector((state: RootState) => state.hud)
  const [currentTime, setCurrentTime] = useState(new Date())

  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentTime(new Date())
    }, 1000)

    return () => clearInterval(timer)
  }, [])

  if (!showTime) return null

  const formatTime = (date: Date) => {
    return date.toLocaleTimeString('en-US', {
      hour: '2-digit',
      minute: '2-digit',
      hour12: false
    })
  }

  const formatDate = (date: Date) => {
    return date.toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric'
    })
  }

  return (
    <motion.div
      initial={{ opacity: 0, x: 50 }}
      animate={{ opacity: 1, x: 0 }}
      transition={{ delay: 0.2 }}
      className="glass-card p-3 min-w-[180px]"
    >
      <div className="space-y-2">
        {/* Time */}
        <div className="flex items-center space-x-3">
          <Clock className="w-4 h-4 text-blue-400" />
          <div className="flex-1">
            <div className="text-white font-bold text-xl">
              {formatTime(currentTime)}
            </div>
          </div>
        </div>

        {/* Date */}
        <div className="flex items-center space-x-3">
          <Calendar className="w-4 h-4 text-gray-400" />
          <div className="flex-1">
            <div className="text-gray-300 text-sm">
              {formatDate(currentTime)}
            </div>
          </div>
        </div>
      </div>
    </motion.div>
  )
}

export default TimeDisplay

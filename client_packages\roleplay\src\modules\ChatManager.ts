/**
 * Client-side Chat Manager
 */

export class ChatManager {
  private isVisible = true;
  private isInputOpen = false;

  /**
   * Initialize chat manager
   */
  public async initialize(): Promise<void> {
    console.log('💬 Initializing Chat Manager...');
    this.setupChatEvents();
  }

  /**
   * Setup chat events
   */
  private setupChatEvents(): void {
    // Chat message from server
    mp.events.add('chat:message', (message: any) => {
      this.displayMessage(message);
    });

    // Disable default RageMP chat
    mp.game.ui.displayHud(false);
    mp.game.ui.displayRadar(true);

    console.log('✅ Chat events setup complete');
  }

  /**
   * Display chat message
   */
  private displayMessage(message: any): void {
    console.log(`💬 [${message.type}] ${message.sender}: ${message.message}`);
    
    // Send message to UI
    mp.events.call('ui:chatMessage', message);
  }

  /**
   * Toggle chat visibility
   */
  public toggle(): void {
    this.isVisible = !this.isVisible;
    console.log('💬 Chat visibility:', this.isVisible);
    
    // Send visibility update to UI
    mp.events.call('ui:chatVisibility', this.isVisible);
  }

  /**
   * Open chat input
   */
  public openInput(): void {
    if (!this.isInputOpen) {
      this.isInputOpen = true;
      console.log('💬 Chat input opened');
      
      // Show cursor for chat input
      mp.gui.cursor.show(true, true);
      
      // Send input open event to UI
      mp.events.call('ui:chatInputOpen');
    }
  }

  /**
   * Close chat input
   */
  public closeInput(): void {
    if (this.isInputOpen) {
      this.isInputOpen = false;
      console.log('💬 Chat input closed');
      
      // Hide cursor
      mp.gui.cursor.show(false, false);
      
      // Send input close event to UI
      mp.events.call('ui:chatInputClose');
    }
  }

  /**
   * Send chat message
   */
  public sendMessage(type: string, message: string): void {
    if (message.trim().length > 0) {
      mp.events.callRemote('chat:sendMessage', {
        type: type,
        message: message.trim()
      });
    }
    
    this.closeInput();
  }

  /**
   * Get chat visibility
   */
  public get visible(): boolean {
    return this.isVisible;
  }

  /**
   * Get input state
   */
  public get inputOpen(): boolean {
    return this.isInputOpen;
  }

  /**
   * Shutdown chat manager
   */
  public async shutdown(): Promise<void> {
    console.log('🛑 Shutting down Chat Manager...');
    this.closeInput();
  }
}

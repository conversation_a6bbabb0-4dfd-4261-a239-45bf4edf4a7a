import { Vector3 } from '../types';
export declare class MathUtils {
    /**
     * Calculate distance between two 3D points
     */
    static distance3D(pos1: Vector3, pos2: Vector3): number;
    /**
     * Calculate distance between two 2D points (ignoring Z axis)
     */
    static distance2D(pos1: Vector3, pos2: Vector3): number;
    /**
     * Generate random number between min and max
     */
    static randomBetween(min: number, max: number): number;
    /**
     * Generate random integer between min and max (inclusive)
     */
    static randomIntBetween(min: number, max: number): number;
    /**
     * Clamp value between min and max
     */
    static clamp(value: number, min: number, max: number): number;
    /**
     * Linear interpolation
     */
    static lerp(start: number, end: number, factor: number): number;
    /**
     * Convert degrees to radians
     */
    static degToRad(degrees: number): number;
    /**
     * Convert radians to degrees
     */
    static radToDeg(radians: number): number;
}
export declare class StringUtils {
    /**
     * Capitalize first letter of string
     */
    static capitalize(str: string): string;
    /**
     * Convert string to title case
     */
    static toTitleCase(str: string): string;
    /**
     * Generate random string of specified length
     */
    static randomString(length: number, chars?: string): string;
    /**
     * Generate UUID v4
     */
    static generateUUID(): string;
    /**
     * Sanitize string for database/security
     */
    static sanitize(str: string): string;
    /**
     * Format number with commas
     */
    static formatNumber(num: number): string;
    /**
     * Format currency
     */
    static formatCurrency(amount: number, symbol?: string): string;
}
export declare class TimeUtils {
    /**
     * Format timestamp to readable string
     */
    static formatTimestamp(timestamp: number | Date): string;
    /**
     * Get time ago string
     */
    static timeAgo(timestamp: number | Date): string;
    /**
     * Format duration in milliseconds to readable string
     */
    static formatDuration(ms: number): string;
}
export declare class ValidationUtils {
    /**
     * Validate email format
     */
    static isValidEmail(email: string): boolean;
    /**
     * Validate username format
     */
    static isValidUsername(username: string): boolean;
    /**
     * Validate password strength
     */
    static isValidPassword(password: string): boolean;
    /**
     * Validate character name
     */
    static isValidCharacterName(name: string): boolean;
    /**
     * Check if string contains only numbers
     */
    static isNumeric(str: string): boolean;
    /**
     * Check if value is within range
     */
    static isInRange(value: number, min: number, max: number): boolean;
}
export declare class ColorUtils {
    /**
     * Convert RGB to hex
     */
    static rgbToHex(r: number, g: number, b: number): string;
    /**
     * Convert hex to RGB
     */
    static hexToRgb(hex: string): {
        r: number;
        g: number;
        b: number;
    } | null;
    /**
     * Generate random color
     */
    static randomColor(): string;
}
export declare class ArrayUtils {
    /**
     * Get random element from array
     */
    static randomElement<T>(array: T[]): T;
    /**
     * Shuffle array
     */
    static shuffle<T>(array: T[]): T[];
    /**
     * Remove duplicates from array
     */
    static unique<T>(array: T[]): T[];
    /**
     * Chunk array into smaller arrays
     */
    static chunk<T>(array: T[], size: number): T[][];
}
//# sourceMappingURL=index.d.ts.map
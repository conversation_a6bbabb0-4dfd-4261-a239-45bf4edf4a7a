/**
 * RageMP Client Packages Entry Point
 * This file loads all client-side scripts and resources
 */

try {
  console.log('🚀 Loading RageMP Roleplay Client...');

  // Load the main roleplay client script
  require('./roleplay/dist/index.js');

  // Load UI resources (CEF)
  // The UI will be loaded through CEF browser instances created by the client script

  console.log('✅ RageMP Client Packages loaded successfully!');
} catch (error) {
  console.error('❌ Failed to load client packages:', error);
  console.error('Stack trace:', error.stack);
}

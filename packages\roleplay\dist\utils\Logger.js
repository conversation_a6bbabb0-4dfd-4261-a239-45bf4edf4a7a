"use strict";
/**
 * Logger utility for consistent logging across the application
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.Logger = exports.LogLevel = void 0;
var LogLevel;
(function (LogLevel) {
    LogLevel[LogLevel["DEBUG"] = 0] = "DEBUG";
    LogLevel[LogLevel["INFO"] = 1] = "INFO";
    LogLevel[LogLevel["WARN"] = 2] = "WARN";
    LogLevel[LogLevel["ERROR"] = 3] = "ERROR";
    LogLevel[LogLevel["SUCCESS"] = 4] = "SUCCESS";
})(LogLevel || (exports.LogLevel = LogLevel = {}));
class Logger {
    static logLevel = LogLevel.INFO;
    /**
     * Set the minimum log level
     */
    static setLogLevel(level) {
        this.logLevel = level;
    }
    /**
     * Get current timestamp
     */
    static getTimestamp() {
        const now = new Date();
        return now.toISOString().replace('T', ' ').substring(0, 19);
    }
    /**
     * Format log message
     */
    static formatMessage(level, message, ...args) {
        const timestamp = this.getTimestamp();
        const formattedArgs = args.length > 0 ? ' ' + args.map(arg => typeof arg === 'object' ? JSON.stringify(arg, null, 2) : String(arg)).join(' ') : '';
        return `[${timestamp}] [${level}] ${message}${formattedArgs}`;
    }
    /**
     * Log debug message
     */
    static debug(message, ...args) {
        if (this.logLevel <= LogLevel.DEBUG) {
            const formatted = this.formatMessage('DEBUG', message, ...args);
            console.log(`\x1b[36m${formatted}\x1b[0m`); // Cyan
        }
    }
    /**
     * Log info message
     */
    static info(message, ...args) {
        if (this.logLevel <= LogLevel.INFO) {
            const formatted = this.formatMessage('INFO', message, ...args);
            console.log(`\x1b[37m${formatted}\x1b[0m`); // White
        }
    }
    /**
     * Log warning message
     */
    static warn(message, ...args) {
        if (this.logLevel <= LogLevel.WARN) {
            const formatted = this.formatMessage('WARN', message, ...args);
            console.log(`\x1b[33m${formatted}\x1b[0m`); // Yellow
        }
    }
    /**
     * Log error message
     */
    static error(message, ...args) {
        if (this.logLevel <= LogLevel.ERROR) {
            const formatted = this.formatMessage('ERROR', message, ...args);
            console.log(`\x1b[31m${formatted}\x1b[0m`); // Red
        }
    }
    /**
     * Log success message
     */
    static success(message, ...args) {
        if (this.logLevel <= LogLevel.SUCCESS) {
            const formatted = this.formatMessage('SUCCESS', message, ...args);
            console.log(`\x1b[32m${formatted}\x1b[0m`); // Green
        }
    }
    /**
     * Log message with custom color
     */
    static custom(color, level, message, ...args) {
        const formatted = this.formatMessage(level, message, ...args);
        console.log(`${color}${formatted}\x1b[0m`);
    }
    /**
     * Log separator line
     */
    static separator(char = '=', length = 50) {
        console.log(char.repeat(length));
    }
    /**
     * Log banner
     */
    static banner(text, char = '=', padding = 2) {
        const totalLength = text.length + (padding * 2);
        const line = char.repeat(totalLength + 4);
        const paddedText = char.repeat(padding) + ' ' + text + ' ' + char.repeat(padding);
        console.log(`\x1b[36m${line}\x1b[0m`);
        console.log(`\x1b[36m${paddedText}\x1b[0m`);
        console.log(`\x1b[36m${line}\x1b[0m`);
    }
}
exports.Logger = Logger;
//# sourceMappingURL=Logger.js.map
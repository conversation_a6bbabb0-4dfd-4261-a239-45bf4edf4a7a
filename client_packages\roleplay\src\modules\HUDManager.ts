/**
 * Client-side HUD Manager
 */

export class HUDManager {
  private isVisible = true;
  private hudElements: Map<string, boolean> = new Map();

  /**
   * Initialize HUD manager
   */
  public async initialize(): Promise<void> {
    console.log('📊 Initializing HUD Manager...');
    this.setupHUDElements();
    this.setupHUDEvents();
  }

  /**
   * Setup HUD elements
   */
  private setupHUDElements(): void {
    // Initialize HUD element visibility
    this.hudElements.set('health', true);
    this.hudElements.set('armor', true);
    this.hudElements.set('stamina', true);
    this.hudElements.set('hunger', true);
    this.hudElements.set('thirst', true);
    this.hudElements.set('money', true);
    this.hudElements.set('time', true);
    this.hudElements.set('location', true);
    this.hudElements.set('speedometer', true);
    this.hudElements.set('minimap', true);

    // Disable default GTA HUD elements
    mp.game.ui.displayHud(false);
    mp.game.ui.displayRadar(true);
    
    console.log('✅ HUD elements setup complete');
  }

  /**
   * Setup HUD events
   */
  private setupHUDEvents(): void {
    // HUD visibility toggle from server
    mp.events.add('hud:toggle', () => {
      this.toggle();
    });

    // HUD element toggle from server
    mp.events.add('hud:toggleElement', (element: string) => {
      this.toggleElement(element);
    });

    // HUD data update from server
    mp.events.add('hud:updateData', (data: any) => {
      this.updateData(data);
    });

    console.log('✅ HUD events setup complete');
  }

  /**
   * Show HUD
   */
  public show(): void {
    if (!this.isVisible) {
      this.isVisible = true;
      console.log('📊 HUD shown');
      
      // Send show event to UI
      mp.events.call('ui:hudShow');
    }
  }

  /**
   * Hide HUD
   */
  public hide(): void {
    if (this.isVisible) {
      this.isVisible = false;
      console.log('📊 HUD hidden');
      
      // Send hide event to UI
      mp.events.call('ui:hudHide');
    }
  }

  /**
   * Toggle HUD visibility
   */
  public toggle(): void {
    this.isVisible = !this.isVisible;
    console.log('📊 HUD visibility:', this.isVisible);
    
    // Send toggle event to UI
    mp.events.call('ui:hudToggle', this.isVisible);
  }

  /**
   * Toggle specific HUD element
   */
  public toggleElement(element: string): void {
    if (this.hudElements.has(element)) {
      const currentState = this.hudElements.get(element)!;
      this.hudElements.set(element, !currentState);
      
      console.log(`📊 HUD element ${element}:`, !currentState);
      
      // Send element toggle event to UI
      mp.events.call('ui:hudToggleElement', {
        element: element,
        visible: !currentState
      });
    }
  }

  /**
   * Update HUD data
   */
  public updateData(data: any): void {
    // Send data update to UI
    mp.events.call('ui:hudUpdateData', data);
  }

  /**
   * Update HUD continuously
   */
  public update(): void {
    if (!this.isVisible) return;

    // Get player data
    const player = mp.players.local;
    if (!player) return;

    // Prepare HUD data
    const hudData = {
      health: player.getHealth(),
      armor: player.getArmour(),
      position: player.position,
      heading: player.getHeading(),
      inVehicle: player.vehicle !== null,
      vehicle: player.vehicle ? {
        speed: player.vehicle.getSpeed() * 3.6, // Convert to km/h
        rpm: player.vehicle.getCurrentRpm() * 8000,
        gear: player.vehicle.getCurrentGear(),
        fuel: 100, // This would come from vehicle data
        engineOn: player.vehicle.getIsEngineRunning()
      } : null
    };

    // Send update to UI
    this.updateData(hudData);
  }

  /**
   * Get HUD visibility
   */
  public get visible(): boolean {
    return this.isVisible;
  }

  /**
   * Get element visibility
   */
  public isElementVisible(element: string): boolean {
    return this.hudElements.get(element) || false;
  }

  /**
   * Shutdown HUD manager
   */
  public async shutdown(): Promise<void> {
    console.log('🛑 Shutting down HUD Manager...');
    this.hide();
    this.hudElements.clear();
  }
}

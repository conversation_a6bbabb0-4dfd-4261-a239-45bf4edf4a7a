export interface IPlayer {
    id: number;
    name: string;
    socialClub: string;
    ip: string;
    ping: number;
    dimension: number;
    position: Vector3;
    heading: number;
    health: number;
    armor: number;
    weapon: number;
    ammo: number;
}
export interface ICharacter {
    id: number;
    playerId: number;
    firstName: string;
    lastName: string;
    age: number;
    gender: 'male' | 'female';
    money: number;
    bankMoney: number;
    job: string;
    jobRank: number;
    level: number;
    experience: number;
    skin: ICharacterSkin;
    position: Vector3;
    dimension: number;
    createdAt: Date;
    updatedAt: Date;
}
export interface ICharacterSkin {
    model: number;
    components: IClothing[];
    props: IProp[];
    faceFeatures: IFaceFeature[];
    headOverlays: IHeadOverlay[];
    eyeColor: number;
    hairColor: number;
    hairHighlight: number;
}
export interface IClothing {
    componentId: number;
    drawableId: number;
    textureId: number;
    paletteId: number;
}
export interface IProp {
    propId: number;
    drawableId: number;
    textureId: number;
}
export interface IFaceFeature {
    index: number;
    scale: number;
}
export interface IHeadOverlay {
    overlayId: number;
    index: number;
    opacity: number;
    colorType: number;
    colorId: number;
    secondColorId: number;
}
export interface Vector3 {
    x: number;
    y: number;
    z: number;
}
export interface ILoginData {
    username: string;
    password: string;
}
export interface IRegisterData {
    username: string;
    email: string;
    password: string;
    confirmPassword: string;
}
export interface IAuthResponse {
    success: boolean;
    message: string;
    token?: string;
    player?: IPlayer;
}
export interface IVehicle {
    id: number;
    model: string;
    position: Vector3;
    rotation: Vector3;
    color1: number;
    color2: number;
    numberPlate: string;
    locked: boolean;
    engine: boolean;
    fuel: number;
    mileage: number;
    ownerId?: number;
}
export interface IInventoryItem {
    id: number;
    name: string;
    description: string;
    weight: number;
    stackable: boolean;
    usable: boolean;
    image?: string;
    metadata?: Record<string, any>;
}
export interface IInventorySlot {
    slot: number;
    item?: IInventoryItem;
    quantity: number;
}
export interface IInventory {
    id: number;
    type: 'player' | 'vehicle' | 'property';
    ownerId: number;
    maxWeight: number;
    maxSlots: number;
    slots: IInventorySlot[];
}
export interface IJob {
    id: number;
    name: string;
    label: string;
    description: string;
    salary: number;
    ranks: IJobRank[];
}
export interface IJobRank {
    rank: number;
    name: string;
    salary: number;
    permissions: string[];
}
export interface IProperty {
    id: number;
    name: string;
    description: string;
    type: 'house' | 'apartment' | 'business' | 'garage';
    price: number;
    position: Vector3;
    interior: Vector3;
    locked: boolean;
    ownerId?: number;
    tenants: number[];
    furniture: IFurniture[];
}
export interface IFurniture {
    id: number;
    model: string;
    position: Vector3;
    rotation: Vector3;
    texture?: string;
}
export interface INotification {
    id: string;
    type: 'success' | 'error' | 'warning' | 'info';
    title: string;
    message: string;
    duration?: number;
    actions?: INotificationAction[];
}
export interface INotificationAction {
    label: string;
    action: string;
    data?: any;
}
export interface IChatMessage {
    id: string;
    type: 'local' | 'global' | 'whisper' | 'shout' | 'me' | 'do' | 'ooc' | 'admin';
    sender: string;
    message: string;
    timestamp: Date;
    range?: number;
}
export interface IAdminAction {
    id: string;
    adminId: number;
    targetId?: number;
    action: string;
    reason?: string;
    timestamp: Date;
    data?: Record<string, any>;
}
//# sourceMappingURL=index.d.ts.map
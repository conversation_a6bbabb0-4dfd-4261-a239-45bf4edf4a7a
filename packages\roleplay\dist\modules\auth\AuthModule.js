"use strict";
/**
 * Authentication Module
 * Handles player login, registration, and session management
 */
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AuthModule = void 0;
const bcryptjs_1 = __importDefault(require("bcryptjs"));
const jsonwebtoken_1 = __importDefault(require("jsonwebtoken"));
const EventManager_1 = require("../../events/EventManager");
const Logger_1 = require("../../utils/Logger");
const UserModel_1 = require("../../database/models/UserModel");
const shared_1 = require("@ragemp-rp/shared");
class AuthModule {
    eventManager;
    jwtSecret;
    authenticatedPlayers = new Map();
    constructor() {
        this.eventManager = EventManager_1.EventManager.getInstance();
        this.jwtSecret = process.env.JWT_SECRET || 'your-secret-key-change-this';
    }
    /**
     * Initialize the authentication module
     */
    async initialize() {
        Logger_1.Logger.info('Initializing Authentication Module...');
        this.setupEventHandlers();
        Logger_1.Logger.success('Authentication Module initialized successfully');
    }
    /**
     * Setup event handlers
     */
    setupEventHandlers() {
        // Login event
        this.eventManager.on(shared_1.ClientToServerEvents.AUTH_LOGIN, this.handleLogin.bind(this));
        // Register event
        this.eventManager.on(shared_1.ClientToServerEvents.AUTH_REGISTER, this.handleRegister.bind(this));
        // Logout event
        this.eventManager.on(shared_1.ClientToServerEvents.AUTH_LOGOUT, this.handleLogout.bind(this));
    }
    /**
     * Handle player join
     */
    onPlayerJoin(player) {
        Logger_1.Logger.info(`Player ${player.name} joined - showing login screen`);
        // Show login UI
        this.eventManager.emitClient(player, shared_1.ServerToClientEvents.SHOW_UI, {
            type: 'login',
            data: {
                serverName: 'RageMP Roleplay Server',
                version: '1.0.0'
            }
        });
    }
    /**
     * Handle login request
     */
    async handleLogin(player, data) {
        try {
            Logger_1.Logger.info(`Login attempt from ${player.name}: ${data.username}`);
            // Validate input
            if (!data.username || !data.password) {
                this.sendAuthResponse(player, {
                    success: false,
                    message: 'Username and password are required'
                });
                return;
            }
            // Find user in database
            const user = await UserModel_1.UserModel.findOne({
                username: data.username.toLowerCase()
            }).select('+password');
            if (!user) {
                this.sendAuthResponse(player, {
                    success: false,
                    message: 'Invalid username or password'
                });
                return;
            }
            // Verify password
            const isValidPassword = await bcryptjs_1.default.compare(data.password, user.password);
            if (!isValidPassword) {
                this.sendAuthResponse(player, {
                    success: false,
                    message: 'Invalid username or password'
                });
                return;
            }
            // Check if user is already online
            if (this.isUserOnline(user._id.toString())) {
                this.sendAuthResponse(player, {
                    success: false,
                    message: 'This account is already logged in'
                });
                return;
            }
            // Generate JWT token
            const token = this.generateToken(user._id.toString(), user.username);
            // Update user's last login
            user.lastLogin = new Date();
            user.lastIP = player.ip;
            await user.save();
            // Store authenticated player
            this.authenticatedPlayers.set(player.id, {
                userId: user._id.toString(),
                username: user.username,
                token: token,
                loginTime: new Date()
            });
            // Send success response
            this.sendAuthResponse(player, {
                success: true,
                message: 'Login successful',
                token: token,
                player: {
                    id: player.id,
                    name: player.name,
                    socialClub: player.socialClub,
                    ip: player.ip,
                    ping: player.ping,
                    dimension: player.dimension,
                    position: player.position,
                    heading: player.heading,
                    health: player.health,
                    armor: player.armour,
                    weapon: player.weapon,
                    ammo: player.getWeaponAmmo(player.weapon)
                }
            });
            Logger_1.Logger.success(`Player ${player.name} logged in successfully as ${user.username}`);
        }
        catch (error) {
            Logger_1.Logger.error('Login error:', error);
            this.sendAuthResponse(player, {
                success: false,
                message: 'An error occurred during login'
            });
        }
    }
    /**
     * Handle registration request
     */
    async handleRegister(player, data) {
        try {
            Logger_1.Logger.info(`Registration attempt from ${player.name}: ${data.username}`);
            // Validate input
            const validation = this.validateRegistrationData(data);
            if (!validation.valid) {
                this.sendAuthResponse(player, {
                    success: false,
                    message: validation.message
                });
                return;
            }
            // Check if username already exists
            const existingUser = await UserModel_1.UserModel.findOne({
                username: data.username.toLowerCase()
            });
            if (existingUser) {
                this.sendAuthResponse(player, {
                    success: false,
                    message: 'Username already exists'
                });
                return;
            }
            // Check if email already exists
            const existingEmail = await UserModel_1.UserModel.findOne({
                email: data.email.toLowerCase()
            });
            if (existingEmail) {
                this.sendAuthResponse(player, {
                    success: false,
                    message: 'Email already registered'
                });
                return;
            }
            // Hash password
            const hashedPassword = await bcryptjs_1.default.hash(data.password, 12);
            // Create new user
            const newUser = new UserModel_1.UserModel({
                username: data.username.toLowerCase(),
                email: data.email.toLowerCase(),
                password: hashedPassword,
                socialClub: player.socialClub,
                registrationIP: player.ip,
                lastIP: player.ip,
                registrationDate: new Date(),
                lastLogin: new Date()
            });
            await newUser.save();
            Logger_1.Logger.success(`New user registered: ${data.username}`);
            this.sendAuthResponse(player, {
                success: true,
                message: 'Registration successful! You can now login.'
            });
        }
        catch (error) {
            Logger_1.Logger.error('Registration error:', error);
            this.sendAuthResponse(player, {
                success: false,
                message: 'An error occurred during registration'
            });
        }
    }
    /**
     * Handle logout request
     */
    handleLogout(player) {
        try {
            const authData = this.authenticatedPlayers.get(player.id);
            if (authData) {
                this.authenticatedPlayers.delete(player.id);
                Logger_1.Logger.info(`Player ${player.name} (${authData.username}) logged out`);
            }
            this.eventManager.emitClient(player, shared_1.ServerToClientEvents.AUTH_LOGOUT);
            // Show login screen again
            this.onPlayerJoin(player);
        }
        catch (error) {
            Logger_1.Logger.error('Logout error:', error);
        }
    }
    /**
     * Validate registration data
     */
    validateRegistrationData(data) {
        if (!data.username || data.username.length < 3 || data.username.length > 20) {
            return { valid: false, message: 'Username must be between 3 and 20 characters' };
        }
        if (!/^[a-zA-Z0-9_]+$/.test(data.username)) {
            return { valid: false, message: 'Username can only contain letters, numbers, and underscores' };
        }
        if (!data.email || !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(data.email)) {
            return { valid: false, message: 'Please enter a valid email address' };
        }
        if (!data.password || data.password.length < 6) {
            return { valid: false, message: 'Password must be at least 6 characters long' };
        }
        if (data.password !== data.confirmPassword) {
            return { valid: false, message: 'Passwords do not match' };
        }
        return { valid: true, message: '' };
    }
    /**
     * Generate JWT token
     */
    generateToken(userId, username) {
        return jsonwebtoken_1.default.sign({ userId, username }, this.jwtSecret, { expiresIn: '24h' });
    }
    /**
     * Verify JWT token
     */
    verifyToken(token) {
        try {
            return jsonwebtoken_1.default.verify(token, this.jwtSecret);
        }
        catch (error) {
            return null;
        }
    }
    /**
     * Check if user is online
     */
    isUserOnline(userId) {
        for (const [playerId, authData] of this.authenticatedPlayers) {
            if (authData.userId === userId) {
                return true;
            }
        }
        return false;
    }
    /**
     * Send authentication response to client
     */
    sendAuthResponse(player, response) {
        this.eventManager.emitClient(player, shared_1.ServerToClientEvents.AUTH_RESPONSE, response);
    }
    /**
     * Get authenticated player data
     */
    getAuthenticatedPlayer(player) {
        return this.authenticatedPlayers.get(player.id);
    }
    /**
     * Check if player is authenticated
     */
    isPlayerAuthenticated(player) {
        return this.authenticatedPlayers.has(player.id);
    }
    /**
     * Shutdown module
     */
    async shutdown() {
        Logger_1.Logger.info('Shutting down Authentication Module...');
        this.authenticatedPlayers.clear();
    }
}
exports.AuthModule = AuthModule;
//# sourceMappingURL=AuthModule.js.map
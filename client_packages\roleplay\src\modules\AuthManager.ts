/**
 * Client-side Authentication Manager
 */

export class AuthManager {
  private isAuthenticated = false;
  private playerData: any = null;

  /**
   * Initialize authentication manager
   */
  public async initialize(): Promise<void> {
    console.log('🔐 Initializing Auth Manager...');
    this.setupAuthEvents();
  }

  /**
   * Setup authentication events
   */
  private setupAuthEvents(): void {
    // Authentication response from server
    mp.events.add('auth:response', (response: any) => {
      this.handleAuthResponse(response);
    });

    // Logout event from server
    mp.events.add('auth:logout', () => {
      this.handleLogout();
    });

    console.log('✅ Auth events setup complete');
  }

  /**
   * Handle authentication response
   */
  private handleAuthResponse(response: any): void {
    if (response.success) {
      this.isAuthenticated = true;
      this.playerData = response.player;
      console.log('✅ Authentication successful');
      
      // Hide login UI and show character selection
      mp.events.call('ui:hide');
      mp.events.call('ui:show', { type: 'characterSelector' });
    } else {
      this.isAuthenticated = false;
      this.playerData = null;
      console.log('❌ Authentication failed:', response.message);
    }
  }

  /**
   * Handle logout
   */
  private handleLogout(): void {
    this.isAuthenticated = false;
    this.playerData = null;
    console.log('🚪 Logged out');
    
    // Show login UI
    mp.events.call('ui:show', { type: 'login' });
  }

  /**
   * Check if player is authenticated
   */
  public get authenticated(): boolean {
    return this.isAuthenticated;
  }

  /**
   * Get player data
   */
  public get player(): any {
    return this.playerData;
  }

  /**
   * Shutdown authentication manager
   */
  public async shutdown(): Promise<void> {
    console.log('🛑 Shutting down Auth Manager...');
    this.isAuthenticated = false;
    this.playerData = null;
  }
}

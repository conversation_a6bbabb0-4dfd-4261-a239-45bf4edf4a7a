import { createSlice, PayloadAction } from '@reduxjs/toolkit'

interface Character {
  id: number
  firstName: string
  lastName: string
  age: number
  gender: 'male' | 'female'
  money: number
  bankMoney: number
  job: string
  jobRank: number
  level: number
  experience: number
  health: number
  armor: number
  hunger: number
  thirst: number
  position: {
    x: number
    y: number
    z: number
  }
  skin: {
    model: number
    components: any[]
    props: any[]
    faceFeatures: any[]
    headOverlays: any[]
    eyeColor: number
    hairColor: number
    hairHighlight: number
  }
}

interface CharacterState {
  characters: Character[]
  activeCharacter: Character | null
  isLoading: boolean
  error: string | null
}

const initialState: CharacterState = {
  characters: [],
  activeCharacter: null,
  isLoading: false,
  error: null,
}

const characterSlice = createSlice({
  name: 'character',
  initialState,
  reducers: {
    setCharacters: (state, action: PayloadAction<Character[]>) => {
      state.characters = action.payload
    },
    setActiveCharacter: (state, action: PayloadAction<Character>) => {
      state.activeCharacter = action.payload
    },
    updateCharacter: (state, action: PayloadAction<Partial<Character>>) => {
      if (state.activeCharacter) {
        state.activeCharacter = { ...state.activeCharacter, ...action.payload }
      }
    },
    updateCharacterStats: (state, action: PayloadAction<{
      health?: number
      armor?: number
      hunger?: number
      thirst?: number
      money?: number
      bankMoney?: number
    }>) => {
      if (state.activeCharacter) {
        Object.assign(state.activeCharacter, action.payload)
      }
    },
    addCharacter: (state, action: PayloadAction<Character>) => {
      state.characters.push(action.payload)
    },
    removeCharacter: (state, action: PayloadAction<number>) => {
      state.characters = state.characters.filter(char => char.id !== action.payload)
    },
    setLoading: (state, action: PayloadAction<boolean>) => {
      state.isLoading = action.payload
    },
    setError: (state, action: PayloadAction<string | null>) => {
      state.error = action.payload
    },
    clearCharacters: (state) => {
      state.characters = []
      state.activeCharacter = null
    },
  },
})

export const {
  setCharacters,
  setActiveCharacter,
  updateCharacter,
  updateCharacterStats,
  addCharacter,
  removeCharacter,
  setLoading,
  setError,
  clearCharacters,
} = characterSlice.actions

export default characterSlice.reducer

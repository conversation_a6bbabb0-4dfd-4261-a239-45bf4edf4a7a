(()=>{"use strict";var e={230:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.EventManager=void 0;class s{static instance;eventHandlers=new Map;constructor(){}static getInstance(){return s.instance||(s.instance=new s),s.instance}async initialize(){console.log("🎯 Initializing Client Event Manager..."),this.setupDefaultEvents()}setupDefaultEvents(){mp.events.add("server:message",e=>{console.log("Server message:",e)}),mp.events.add("cef:ready",()=>{console.log("CEF is ready")}),console.log("✅ Default events setup complete")}on(e,t){this.eventHandlers.has(e)||this.eventHandlers.set(e,[]),this.eventHandlers.get(e).push(t),mp.events.add(e,t),console.log(`📡 Registered handler for event: ${e}`)}off(e,t){if(this.eventHandlers.has(e))if(t){const s=this.eventHandlers.get(e),a=s.indexOf(t);a>-1&&(s.splice(a,1),mp.events.remove(e,t))}else this.eventHandlers.get(e).forEach(t=>mp.events.remove(e,t)),this.eventHandlers.delete(e)}callServer(e,...t){mp.events.callRemote(e,...t)}callBrowser(e,t,...s){e&&e.execute&&e.execute(`\n        if (window.mp && window.mp.trigger) {\n          window.mp.trigger('${t}', ${JSON.stringify(s)});\n        }\n      `)}async shutdown(){console.log("🛑 Shutting down Client Event Manager...");for(const[e,t]of this.eventHandlers)t.forEach(t=>mp.events.remove(e,t));this.eventHandlers.clear()}}t.EventManager=s},281:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.HUDManager=void 0,t.HUDManager=class{isVisible=!0;hudElements=new Map;async initialize(){console.log("📊 Initializing HUD Manager..."),this.setupHUDElements(),this.setupHUDEvents()}setupHUDElements(){this.hudElements.set("health",!0),this.hudElements.set("armor",!0),this.hudElements.set("stamina",!0),this.hudElements.set("hunger",!0),this.hudElements.set("thirst",!0),this.hudElements.set("money",!0),this.hudElements.set("time",!0),this.hudElements.set("location",!0),this.hudElements.set("speedometer",!0),this.hudElements.set("minimap",!0),mp.game.ui.displayHud(!1),mp.game.ui.displayRadar(!0),console.log("✅ HUD elements setup complete")}setupHUDEvents(){mp.events.add("hud:toggle",()=>{this.toggle()}),mp.events.add("hud:toggleElement",e=>{this.toggleElement(e)}),mp.events.add("hud:updateData",e=>{this.updateData(e)}),console.log("✅ HUD events setup complete")}show(){this.isVisible||(this.isVisible=!0,console.log("📊 HUD shown"),mp.events.call("ui:hudShow"))}hide(){this.isVisible&&(this.isVisible=!1,console.log("📊 HUD hidden"),mp.events.call("ui:hudHide"))}toggle(){this.isVisible=!this.isVisible,console.log("📊 HUD visibility:",this.isVisible),mp.events.call("ui:hudToggle",this.isVisible)}toggleElement(e){if(this.hudElements.has(e)){const t=this.hudElements.get(e);this.hudElements.set(e,!t),console.log(`📊 HUD element ${e}:`,!t),mp.events.call("ui:hudToggleElement",{element:e,visible:!t})}}updateData(e){mp.events.call("ui:hudUpdateData",e)}update(){if(!this.isVisible)return;const e=mp.players.local;if(!e)return;const t={health:e.getHealth(),armor:e.getArmour(),position:e.position,heading:e.getHeading(),inVehicle:null!==e.vehicle,vehicle:e.vehicle?{speed:3.6*e.vehicle.getSpeed(),rpm:8e3*e.vehicle.getCurrentRpm(),gear:e.vehicle.getCurrentGear(),fuel:100,engineOn:e.vehicle.getIsEngineRunning()}:null};this.updateData(t)}get visible(){return this.isVisible}isElementVisible(e){return this.hudElements.get(e)||!1}async shutdown(){console.log("🛑 Shutting down HUD Manager..."),this.hide(),this.hudElements.clear()}}},404:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.AuthManager=void 0,t.AuthManager=class{isAuthenticated=!1;playerData=null;async initialize(){console.log("🔐 Initializing Auth Manager..."),this.setupAuthEvents()}setupAuthEvents(){mp.events.add("auth:response",e=>{this.handleAuthResponse(e)}),mp.events.add("auth:logout",()=>{this.handleLogout()}),console.log("✅ Auth events setup complete")}handleAuthResponse(e){e.success?(this.isAuthenticated=!0,this.playerData=e.player,console.log("✅ Authentication successful"),mp.events.call("ui:hide"),mp.events.call("ui:show",{type:"characterSelector"})):(this.isAuthenticated=!1,this.playerData=null,console.log("❌ Authentication failed:",e.message))}handleLogout(){this.isAuthenticated=!1,this.playerData=null,console.log("🚪 Logged out"),mp.events.call("ui:show",{type:"login"})}get authenticated(){return this.isAuthenticated}get player(){return this.playerData}async shutdown(){console.log("🛑 Shutting down Auth Manager..."),this.isAuthenticated=!1,this.playerData=null}}},620:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.ChatManager=void 0,t.ChatManager=class{isVisible=!0;isInputOpen=!1;async initialize(){console.log("💬 Initializing Chat Manager..."),this.setupChatEvents()}setupChatEvents(){mp.events.add("chat:message",e=>{this.displayMessage(e)}),mp.game.ui.displayHud(!1),mp.game.ui.displayRadar(!0),console.log("✅ Chat events setup complete")}displayMessage(e){console.log(`💬 [${e.type}] ${e.sender}: ${e.message}`),mp.events.call("ui:chatMessage",e)}toggle(){this.isVisible=!this.isVisible,console.log("💬 Chat visibility:",this.isVisible),mp.events.call("ui:chatVisibility",this.isVisible)}openInput(){this.isInputOpen||(this.isInputOpen=!0,console.log("💬 Chat input opened"),mp.gui.cursor.show(!0,!0),mp.events.call("ui:chatInputOpen"))}closeInput(){this.isInputOpen&&(this.isInputOpen=!1,console.log("💬 Chat input closed"),mp.gui.cursor.show(!1,!1),mp.events.call("ui:chatInputClose"))}sendMessage(e,t){t.trim().length>0&&mp.events.callRemote("chat:sendMessage",{type:e,message:t.trim()}),this.closeInput()}get visible(){return this.isVisible}get inputOpen(){return this.isInputOpen}async shutdown(){console.log("🛑 Shutting down Chat Manager..."),this.closeInput()}}},647:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.CharacterManager=void 0,t.CharacterManager=class{activeCharacter=null;characters=[];async initialize(){console.log("👤 Initializing Character Manager..."),this.setupCharacterEvents()}setupCharacterEvents(){mp.events.add("character:data",e=>{this.setActiveCharacter(e)}),mp.events.add("character:list",e=>{this.setCharacters(e)}),mp.events.add("character:update",e=>{this.updateCharacter(e)}),console.log("✅ Character events setup complete")}setActiveCharacter(e){this.activeCharacter=e,console.log("👤 Active character set:",e.firstName,e.lastName),mp.events.call("ui:hide")}setCharacters(e){this.characters=e,console.log("👥 Characters loaded:",e.length)}updateCharacter(e){this.activeCharacter&&(Object.assign(this.activeCharacter,e),console.log("👤 Character updated"))}onPlayerDeath(e,t){console.log("💀 Player died, reason:",e)}onPlayerChat(e,t){mp.events.callRemote("chat:sendMessage",{type:"local",message:t})}get character(){return this.activeCharacter}get charactersList(){return this.characters}async shutdown(){console.log("🛑 Shutting down Character Manager..."),this.activeCharacter=null,this.characters=[]}}},807:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.UIManager=void 0;class s{static instance;browsers=new Map;activeBrowser=null;constructor(){}static getInstance(){return s.instance||(s.instance=new s),s.instance}async initialize(){console.log("🖥️ Initializing UI Manager..."),this.setupUIEvents()}setupUIEvents(){mp.events.add("ui:show",e=>{this.showUI(e.type,e.data)}),mp.events.add("ui:hide",()=>{this.hideUI()}),mp.events.add("ui:updateData",e=>{this.updateUIData(e)}),console.log("✅ UI events setup complete")}getBrowser(e){if(!this.browsers.has(e)){const t=mp.browsers.new("package://ui/dist/index.html");this.browsers.set(e,t),t.execute("\n        window.mp = window.mp || {};\n        window.mp.trigger = function(eventName, ...args) {\n          if (window.mp.events && window.mp.events.call) {\n            window.mp.events.call('browserToClient', eventName, ...args);\n          }\n        };\n      ")}return this.browsers.get(e)}showUI(e,t){console.log(`🖥️ Showing UI: ${e}`);const s=this.getBrowser("main");this.activeBrowser=s,mp.gui.cursor.show(!0,!0),mp.game.ui.displayRadar(!1),s.execute(`\n      if (window.mp && window.mp.events) {\n        window.mp.events.add('ui:show', ${JSON.stringify({type:e,data:t})});\n      }\n    `)}hideUI(){console.log("🖥️ Hiding UI"),this.activeBrowser&&(mp.gui.cursor.show(!1,!1),mp.game.ui.displayRadar(!0),this.activeBrowser.execute("\n        if (window.mp && window.mp.events) {\n          window.mp.events.add('ui:hide', {});\n        }\n      "),this.activeBrowser=null)}updateUIData(e){this.activeBrowser&&this.activeBrowser.execute(`\n        if (window.mp && window.mp.events) {\n          window.mp.events.add('ui:updateData', ${JSON.stringify(e)});\n        }\n      `)}handleCEFEvent(e,t){switch(console.log(`🌐 CEF Event: ${e}`,t),e){case"ui:loginSubmit":mp.events.callRemote("auth:login",t);break;case"ui:registerSubmit":mp.events.callRemote("auth:register",t);break;case"ui:characterCreate":mp.events.callRemote("character:create",t);break;case"ui:characterSelect":mp.events.callRemote("character:select",t);break;case"ui:characterDelete":mp.events.callRemote("character:delete",t);break;case"ui:close":this.hideUI();break;default:console.warn(`Unknown CEF event: ${e}`)}}closeAll(){console.log("🖥️ Closing all UI"),this.hideUI()}async shutdown(){console.log("🛑 Shutting down UI Manager...");for(const[e,t]of this.browsers)t&&t.destroy&&t.destroy();this.browsers.clear(),this.activeBrowser=null,mp.gui.cursor.show(!1,!1),mp.game.ui.displayRadar(!0)}}t.UIManager=s},813:(e,t)=>{var s;Object.defineProperty(t,"__esModule",{value:!0}),t.Logger=t.LogLevel=void 0,function(e){e[e.DEBUG=0]="DEBUG",e[e.INFO=1]="INFO",e[e.WARN=2]="WARN",e[e.ERROR=3]="ERROR",e[e.SUCCESS=4]="SUCCESS"}(s||(t.LogLevel=s={}));class a{static logLevel=s.INFO;static setLogLevel(e){this.logLevel=e}static getTimestamp(){return(new Date).toISOString().replace("T"," ").substring(0,19)}static formatMessage(e,t,...s){return`[${this.getTimestamp()}] [CLIENT] [${e}] ${t}${s.length>0?" "+s.map(e=>"object"==typeof e?JSON.stringify(e,null,2):String(e)).join(" "):""}`}static debug(e,...t){if(this.logLevel<=s.DEBUG){const s=this.formatMessage("DEBUG",e,...t);console.log(s)}}static info(e,...t){if(this.logLevel<=s.INFO){const s=this.formatMessage("INFO",e,...t);console.log(s)}}static warn(e,...t){if(this.logLevel<=s.WARN){const s=this.formatMessage("WARN",e,...t);console.warn(s)}}static error(e,...t){if(this.logLevel<=s.ERROR){const s=this.formatMessage("ERROR",e,...t);console.error(s)}}static success(e,...t){if(this.logLevel<=s.SUCCESS){const s=this.formatMessage("SUCCESS",e,...t);console.log(s)}}}t.Logger=a}},t={};function s(a){var n=t[a];if(void 0!==n)return n.exports;var i=t[a]={exports:{}};return e[a](i,i.exports,s),i.exports}(()=>{const e=s(230),t=s(807),a=s(404),n=s(647),i=s(620),l=s(281),o=s(813);class r{static instance;managers=new Map;isInitialized=!1;constructor(){if(r.instance)return r.instance;r.instance=this}async initialize(){try{o.Logger.info("🚀 Starting RageMP Roleplay Client..."),await this.initializeManagers(),this.setupGlobalEvents(),this.isInitialized=!0,o.Logger.success("✅ RageMP Roleplay Client started successfully!")}catch(e){throw o.Logger.error("❌ Failed to start client:",e),e}}async initializeManagers(){o.Logger.info("🔧 Initializing managers...");const s=[{name:"event",instance:e.EventManager.getInstance()},{name:"ui",instance:t.UIManager.getInstance()},{name:"auth",instance:new a.AuthManager},{name:"character",instance:new n.CharacterManager},{name:"chat",instance:new i.ChatManager},{name:"hud",instance:new l.HUDManager}];for(const{name:e,instance:t}of s)try{o.Logger.info(`  📦 Loading ${e} manager...`),await t.initialize(),this.managers.set(e,t),o.Logger.success(`  ✅ ${e} manager loaded`)}catch(t){throw o.Logger.error(`  ❌ Failed to load ${e} manager:`,t),t}o.Logger.success("✅ All managers initialized successfully")}setupGlobalEvents(){o.Logger.info("🎯 Setting up global event handlers..."),mp.events.add("playerSpawn",()=>{o.Logger.info("Player spawned");const e=this.managers.get("hud");e&&e.show()}),mp.events.add("playerDeath",(e,t,s)=>{o.Logger.info(`Player died (reason: ${t})`);const a=this.managers.get("character");a&&a.onPlayerDeath(t,s)}),mp.events.add("render",()=>{const e=this.managers.get("hud");e&&e.update()}),mp.events.add("keydown",e=>{this.handleKeyDown(e)}),mp.events.add("keyup",e=>{this.handleKeyUp(e)}),mp.events.add("cef:emit",(e,t)=>{const s=this.managers.get("ui");s&&s.handleCEFEvent(e,t)}),mp.events.add("rpc:call",(e,t,...s)=>{this.handleRPCCall(e,t,...s)}),o.Logger.success("✅ Global event handlers setup complete")}handleKeyDown(e){if(112===e){const e=this.managers.get("chat");e&&e.toggle()}if(113===e){const e=this.managers.get("hud");e&&e.toggle()}if(84===e){const e=this.managers.get("chat");e&&e.openInput()}if(27===e){const e=this.managers.get("ui");e&&e.closeAll()}}handleKeyUp(e){}async handleRPCCall(e,t,...s){try{let s,a=null;switch(e){case"getPlayerPosition":a=mp.players.local.position;break;case"getPlayerHealth":a=mp.players.local.health;break;case"isPlayerInVehicle":a=null!==mp.players.local.vehicle;break;default:s=`Unknown RPC call: ${e}`}mp.events.callRemote(`rpc:response:${t}`,a,s)}catch(e){mp.events.callRemote(`rpc:response:${t}`,null,e.message)}}getManager(e){return this.managers.get(e)}get initialized(){return this.isInitialized}async shutdown(){o.Logger.info("🛑 Shutting down client...");for(const[e,t]of this.managers)try{t.shutdown&&(await t.shutdown(),o.Logger.info(`  ✅ ${e} manager shutdown complete`))}catch(t){o.Logger.error(`  ❌ Error shutting down ${e} manager:`,t)}o.Logger.success("✅ Client shutdown complete")}}let c;(async()=>{try{c=new r,await c.initialize()}catch(e){o.Logger.error("Failed to initialize client:",e)}})()})()})();
//# sourceMappingURL=index.js.map
import { useSelector } from 'react-redux'
import { motion } from 'framer-motion'
import { Card, CardBody, Button } from '@nextui-org/react'
import { Plus, Play, Trash2 } from 'lucide-react'
import { RootState } from '@/store'

const CharacterSelector = () => {
  const { characters } = useSelector((state: RootState) => state.character)

  const handleCreateCharacter = () => {
    if (window.mp) {
      window.mp.trigger('ui:characterCreate')
    }
  }

  const handleSelectCharacter = (characterId: number) => {
    if (window.mp) {
      window.mp.trigger('ui:characterSelect', characterId)
    }
  }

  const handleDeleteCharacter = (characterId: number) => {
    if (window.mp) {
      window.mp.trigger('ui:characterDelete', characterId)
    }
  }

  return (
    <div className="fixed inset-0 flex items-center justify-center p-4">
      <motion.div
        initial={{ opacity: 0, scale: 0.9 }}
        animate={{ opacity: 1, scale: 1 }}
        exit={{ opacity: 0, scale: 0.9 }}
        transition={{ duration: 0.3 }}
        className="w-full max-w-4xl"
      >
        <Card className="glass-card-dark">
          <CardBody className="p-8">
            <div className="text-center mb-8">
              <h1 className="text-3xl font-bold text-white mb-2">
                Select Character
              </h1>
              <p className="text-gray-300">
                Choose a character to play with
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {/* Create New Character */}
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.1 }}
              >
                <Card 
                  className="glass-card cursor-pointer hover:scale-105 transition-transform"
                  isPressable
                  onPress={handleCreateCharacter}
                >
                  <CardBody className="p-6 text-center">
                    <Plus className="w-12 h-12 mx-auto mb-4 text-primary-400" />
                    <h3 className="text-xl font-bold text-white mb-2">
                      Create New
                    </h3>
                    <p className="text-gray-300 text-sm">
                      Start your roleplay journey
                    </p>
                  </CardBody>
                </Card>
              </motion.div>

              {/* Existing Characters */}
              {characters.map((character, index) => (
                <motion.div
                  key={character.id}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.2 + index * 0.1 }}
                >
                  <Card className="glass-card">
                    <CardBody className="p-6">
                      <div className="text-center mb-4">
                        <div className="w-16 h-16 mx-auto mb-3 bg-gradient-to-br from-primary-400 to-primary-600 rounded-full flex items-center justify-center">
                          <span className="text-white font-bold text-xl">
                            {character.firstName.charAt(0)}{character.lastName.charAt(0)}
                          </span>
                        </div>
                        <h3 className="text-lg font-bold text-white">
                          {character.firstName} {character.lastName}
                        </h3>
                        <p className="text-gray-300 text-sm">
                          Level {character.level} • {character.job}
                        </p>
                      </div>

                      <div className="space-y-2 mb-4 text-sm">
                        <div className="flex justify-between text-gray-300">
                          <span>Age:</span>
                          <span>{character.age}</span>
                        </div>
                        <div className="flex justify-between text-gray-300">
                          <span>Money:</span>
                          <span>${character.money.toLocaleString()}</span>
                        </div>
                        <div className="flex justify-between text-gray-300">
                          <span>Bank:</span>
                          <span>${character.bankMoney.toLocaleString()}</span>
                        </div>
                      </div>

                      <div className="flex space-x-2">
                        <Button
                          className="flex-1 btn-glass bg-green-500/20 border-green-400 hover:bg-green-500/30"
                          size="sm"
                          onPress={() => handleSelectCharacter(character.id)}
                        >
                          <Play className="w-4 h-4 mr-1" />
                          Play
                        </Button>
                        <Button
                          className="btn-glass bg-red-500/20 border-red-400 hover:bg-red-500/30"
                          size="sm"
                          isIconOnly
                          onPress={() => handleDeleteCharacter(character.id)}
                        >
                          <Trash2 className="w-4 h-4" />
                        </Button>
                      </div>
                    </CardBody>
                  </Card>
                </motion.div>
              ))}
            </div>
          </CardBody>
        </Card>
      </motion.div>
    </div>
  )
}

export default CharacterSelector

/**
 * RageMP Roleplay Client
 * Main entry point for the client-side application
 */

import { EventManager } from './events/EventManager';
import { UIManager } from './ui/UIManager';
import { AuthManager } from './modules/AuthManager';
import { CharacterManager } from './modules/CharacterManager';
import { ChatManager } from './modules/ChatManager';
import { HUDManager } from './modules/HUDManager';
import { Logger } from './utils/Logger';

class RoleplayClient {
  private static instance: RoleplayClient;
  private managers: Map<string, any> = new Map();
  private isInitialized = false;

  constructor() {
    if (RoleplayClient.instance) {
      return RoleplayClient.instance;
    }
    RoleplayClient.instance = this;
  }

  /**
   * Initialize the client
   */
  public async initialize(): Promise<void> {
    try {
      Logger.info('🚀 Starting RageMP Roleplay Client...');

      // Initialize core managers
      await this.initializeManagers();

      // Setup global event handlers
      this.setupGlobalEvents();

      this.isInitialized = true;
      Logger.success('✅ RageMP Roleplay Client started successfully!');
      
    } catch (error) {
      Logger.error('❌ Failed to start client:', error);
      throw error;
    }
  }

  /**
   * Initialize all managers
   */
  private async initializeManagers(): Promise<void> {
    Logger.info('🔧 Initializing managers...');

    const managerClasses = [
      { name: 'event', instance: EventManager.getInstance() },
      { name: 'ui', instance: UIManager.getInstance() },
      { name: 'auth', instance: new AuthManager() },
      { name: 'character', instance: new CharacterManager() },
      { name: 'chat', instance: new ChatManager() },
      { name: 'hud', instance: new HUDManager() }
    ];

    for (const { name, instance } of managerClasses) {
      try {
        Logger.info(`  📦 Loading ${name} manager...`);
        await instance.initialize();
        this.managers.set(name, instance);
        Logger.success(`  ✅ ${name} manager loaded`);
      } catch (error) {
        Logger.error(`  ❌ Failed to load ${name} manager:`, error);
        throw error;
      }
    }

    Logger.success('✅ All managers initialized successfully');
  }

  /**
   * Setup global event handlers
   */
  private setupGlobalEvents(): void {
    Logger.info('🎯 Setting up global event handlers...');

    // Player spawn event
    mp.events.add('playerSpawn', () => {
      Logger.info('Player spawned');
      
      // Initialize HUD
      const hudManager = this.managers.get('hud') as HUDManager;
      if (hudManager) {
        hudManager.show();
      }
    });

    // Player death event
    mp.events.add('playerDeath', (player: PlayerMp, reason: number, killer: PlayerMp) => {
      Logger.info(`Player died (reason: ${reason})`);
      
      // Handle death through character manager
      const characterManager = this.managers.get('character') as CharacterManager;
      if (characterManager) {
        characterManager.onPlayerDeath(reason, killer);
      }
    });

    // Render event for continuous updates
    mp.events.add('render', () => {
      // Update HUD
      const hudManager = this.managers.get('hud') as HUDManager;
      if (hudManager) {
        hudManager.update();
      }
    });

    // Key events
    mp.events.add('keydown', (keyCode: number) => {
      this.handleKeyDown(keyCode);
    });

    mp.events.add('keyup', (keyCode: number) => {
      this.handleKeyUp(keyCode);
    });

    // CEF events
    mp.events.add('cef:emit', (eventName: string, data: any) => {
      const uiManager = this.managers.get('ui') as UIManager;
      if (uiManager) {
        uiManager.handleCEFEvent(eventName, data);
      }
    });

    // RPC call handler
    mp.events.add('rpc:call', (eventName: string, callId: string, ...args: any[]) => {
      this.handleRPCCall(eventName, callId, ...args);
    });

    Logger.success('✅ Global event handlers setup complete');
  }

  /**
   * Handle key down events
   */
  private handleKeyDown(keyCode: number): void {
    // F1 - Toggle chat
    if (keyCode === 112) { // F1
      const chatManager = this.managers.get('chat') as ChatManager;
      if (chatManager) {
        chatManager.toggle();
      }
    }

    // F2 - Toggle HUD
    if (keyCode === 113) { // F2
      const hudManager = this.managers.get('hud') as HUDManager;
      if (hudManager) {
        hudManager.toggle();
      }
    }

    // T - Open chat input
    if (keyCode === 84) { // T
      const chatManager = this.managers.get('chat') as ChatManager;
      if (chatManager) {
        chatManager.openInput();
      }
    }

    // ESC - Close UI
    if (keyCode === 27) { // ESC
      const uiManager = this.managers.get('ui') as UIManager;
      if (uiManager) {
        uiManager.closeAll();
      }
    }
  }

  /**
   * Handle key up events
   */
  private handleKeyUp(keyCode: number): void {
    // Handle key up events if needed
  }

  /**
   * Handle RPC calls from server
   */
  private async handleRPCCall(eventName: string, callId: string, ...args: any[]): Promise<void> {
    try {
      let result: any = null;
      let error: string | undefined = undefined;

      // Route RPC calls to appropriate managers
      switch (eventName) {
        case 'getPlayerPosition':
          result = mp.players.local.position;
          break;
        
        case 'getPlayerHealth':
          result = mp.players.local.health;
          break;
        
        case 'isPlayerInVehicle':
          result = mp.players.local.vehicle !== null;
          break;
        
        default:
          error = `Unknown RPC call: ${eventName}`;
          break;
      }

      // Send response back to server
      mp.events.callRemote(`rpc:response:${callId}`, result, error);

    } catch (err) {
      mp.events.callRemote(`rpc:response:${callId}`, null, (err as Error).message);
    }
  }

  /**
   * Get manager instance
   */
  public getManager<T>(name: string): T | undefined {
    return this.managers.get(name) as T;
  }

  /**
   * Check if client is initialized
   */
  public get initialized(): boolean {
    return this.isInitialized;
  }

  /**
   * Shutdown the client gracefully
   */
  public async shutdown(): Promise<void> {
    Logger.info('🛑 Shutting down client...');

    // Shutdown managers
    for (const [name, manager] of this.managers) {
      try {
        if (manager.shutdown) {
          await manager.shutdown();
          Logger.info(`  ✅ ${name} manager shutdown complete`);
        }
      } catch (error) {
        Logger.error(`  ❌ Error shutting down ${name} manager:`, error);
      }
    }
    
    Logger.success('✅ Client shutdown complete');
  }
}

// Global client instance
let clientInstance: RoleplayClient;

// Initialize client when script loads
(async () => {
  try {
    clientInstance = new RoleplayClient();
    await clientInstance.initialize();
  } catch (error) {
    Logger.error('Failed to initialize client:', error);
  }
})();

// Export client instance for other modules
export { clientInstance as Client };

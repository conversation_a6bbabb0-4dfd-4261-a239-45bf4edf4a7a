# 🎮 RageMP Roleplay Server

یک سرور رول‌پلی پیشرفته و مدرن برای RageMP با استفاده از TypeScript، React و طراحی Glass Morphism.

## ✨ ویژگی‌ها

- 🔐 **سیستم احراز هویت پیشرفته** - Login/Register با امنیت بالا
- 👤 **مدیریت کاراکتر** - ایجاد، انتخاب و سفارشی‌سازی کاراکتر
- 💰 **سیستم اقتصادی** - مدیریت پول، بانک و تراکنش‌ها
- 🏠 **سیستم املاک** - خرید و فروش خانه و کسب‌وکار
- 💼 **سیستم شغل‌ها** - انواع مختلف شغل با سیستم رتبه‌بندی
- 🎒 **سیستم اینونتری** - مدیریت اقلام با drag & drop
- 🎨 **UI مدرن** - طراحی Glass Morphism با انیمیشن‌های روان
- 📱 **Responsive Design** - سازگار با تمام اندازه‌های صفحه

## 🏗️ معماری

```
ragemp-roleplay-server/
├── packages/roleplay/          # Server-side (TypeScript)
│   ├── src/
│   │   ├── modules/           # ماژول‌های مختلف (Auth, Character, Economy, etc.)
│   │   ├── database/          # مدل‌های MongoDB
│   │   ├── events/            # مدیریت رویدادها
│   │   └── utils/             # توابع کمکی
├── client_packages/roleplay/   # Client-side (TypeScript)
│   ├── src/
│   │   ├── modules/           # ماژول‌های کلاینت
│   │   ├── events/            # مدیریت رویدادها
│   │   ├── ui/                # مدیریت CEF
│   │   └── utils/             # توابع کمکی
├── client_packages/ui/         # React UI (CEF)
│   ├── src/
│   │   ├── components/        # کامپوننت‌های قابل استفاده مجدد
│   │   ├── pages/             # صفحات اصلی
│   │   ├── store/             # Redux store
│   │   └── styles/            # TailwindCSS styles
├── shared/                     # Types و utilities مشترک
└── package.json               # Root workspace
```

## 🚀 نصب و راه‌اندازی

### پیش‌نیازها

- Node.js 18+ 
- MongoDB
- RageMP Server

### مراحل نصب

1. **کلون کردن پروژه:**
```bash
git clone <repository-url>
cd ragemp-roleplay-server
```

2. **نصب dependencies:**
```bash
npm install
```

3. **تنظیم متغیرهای محیطی:**
```bash
cp packages/roleplay/.env.example packages/roleplay/.env
```

4. **ویرایش فایل .env:**
```env
MONGODB_URI=mongodb://localhost:27017/ragemp_roleplay
JWT_SECRET=your-super-secret-jwt-key
SERVER_NAME=RageMP Roleplay Server
```

5. **Build کردن پروژه:**
```bash
npm run build
```

6. **راه‌اندازی سرور:**
- فایل‌های build شده را در فولدر RageMP server کپی کنید
- سرور RageMP را اجرا کنید

## 🛠️ تکنولوژی‌های استفاده شده

### Backend (Server-side)
- **TypeScript** - Type safety و developer experience بهتر
- **Node.js** - Runtime environment
- **MongoDB** - پایگاه داده NoSQL
- **Mongoose** - ODM برای MongoDB
- **bcryptjs** - Hash کردن رمز عبور
- **jsonwebtoken** - JWT authentication

### Frontend (Client-side)
- **TypeScript** - Type safety
- **React** - UI framework
- **Redux Toolkit** - State management
- **TailwindCSS** - Utility-first CSS framework
- **NextUI** - Modern React UI library
- **Framer Motion** - Animation library

### Build Tools
- **Webpack** - Module bundler
- **Vite** - Fast build tool برای React
- **TypeScript Compiler** - Type checking و compilation

## 📱 صفحات UI

### صفحات احراز هویت
- **Login Page** - ورود کاربران با validation
- **Register Page** - ثبت‌نام کاربران جدید

### صفحات کاراکتر
- **Character Selector** - انتخاب کاراکتر از لیست
- **Character Creator** - ایجاد کاراکتر جدید با customization

### HUD Elements
- **Health Bar** - نمایش سلامتی، زره، استامینا، گرسنگی، تشنگی
- **Money Display** - نمایش پول نقد و بانک
- **Time Display** - نمایش زمان و تاریخ
- **Location Display** - نمایش موقعیت فعلی
- **Speedometer** - نمایش سرعت و اطلاعات وسیله نقلیه

## 🎨 طراحی

پروژه از **Glass Morphism** design system استفاده می‌کند که شامل:

- شفافیت و blur effects
- انیمیشن‌های روان
- رنگ‌بندی مدرن
- Typography زیبا
- Responsive design

## 🔧 Development

### اجرای محیط توسعه:

```bash
# Server development
npm run dev:server

# Client development  
npm run dev:client

# UI development
npm run dev:ui

# همه موارد به صورت همزمان
npm run dev
```

### Build کردن:

```bash
# Build همه چیز
npm run build

# Build جداگانه
npm run build:server
npm run build:client
npm run build:ui
```

## 📝 مستندات

### Event System
سیستم رویدادها بین server، client و UI:

```typescript
// Server to Client
mp.events.call('ui:show', { type: 'login' });

// Client to Server  
mp.events.callRemote('auth:login', loginData);

// UI to Client
window.mp.trigger('ui:loginSubmit', formData);
```

### State Management
Redux store برای مدیریت state:

```typescript
// Auth state
const { isAuthenticated, user } = useSelector(state => state.auth);

// UI state
const { activeUI, theme } = useSelector(state => state.ui);
```

## 🤝 مشارکت

1. Fork کنید
2. Feature branch ایجاد کنید (`git checkout -b feature/amazing-feature`)
3. تغییرات را commit کنید (`git commit -m 'Add amazing feature'`)
4. Branch را push کنید (`git push origin feature/amazing-feature`)
5. Pull Request ایجاد کنید

## 📄 لایسنس

این پروژه تحت لایسنس MIT منتشر شده است.

## 👥 تیم توسعه

- **Backend Developer** - سیستم‌های server-side
- **Frontend Developer** - UI/UX و client-side
- **DevOps** - راه‌اندازی و deployment

---

**نکته:** این پروژه در حال توسعه است و ویژگی‌های جدید به‌طور مداوم اضافه می‌شوند.

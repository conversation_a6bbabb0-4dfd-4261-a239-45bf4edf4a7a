"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.VehicleClass = exports.FactionType = exports.TimePeriod = exports.WeatherType = exports.InteractionType = exports.UIType = exports.NotificationType = exports.AdminLevel = exports.ChatType = exports.ItemType = exports.PropertyType = exports.JobType = exports.VehicleState = exports.Gender = exports.PlayerState = void 0;
// Player states
var PlayerState;
(function (PlayerState) {
    PlayerState[PlayerState["OFFLINE"] = 0] = "OFFLINE";
    PlayerState[PlayerState["ONLINE"] = 1] = "ONLINE";
    PlayerState[PlayerState["AWAY"] = 2] = "AWAY";
    PlayerState[PlayerState["BUSY"] = 3] = "BUSY";
    PlayerState[PlayerState["DEAD"] = 4] = "DEAD";
    PlayerState[PlayerState["UNCONSCIOUS"] = 5] = "UNCONSCIOUS";
})(PlayerState || (exports.PlayerState = PlayerState = {}));
// Character genders
var Gender;
(function (Gender) {
    Gender["MALE"] = "male";
    Gender["FEMALE"] = "female";
})(Gender || (exports.Gender = Gender = {}));
// Vehicle states
var VehicleState;
(function (VehicleState) {
    VehicleState[VehicleState["PARKED"] = 0] = "PARKED";
    VehicleState[VehicleState["DRIVING"] = 1] = "DRIVING";
    VehicleState[VehicleState["DAMAGED"] = 2] = "DAMAGED";
    VehicleState[VehicleState["DESTROYED"] = 3] = "DESTROYED";
    VehicleState[VehicleState["IMPOUNDED"] = 4] = "IMPOUNDED";
})(VehicleState || (exports.VehicleState = VehicleState = {}));
// Job types
var JobType;
(function (JobType) {
    JobType["UNEMPLOYED"] = "unemployed";
    JobType["POLICE"] = "police";
    JobType["MEDIC"] = "medic";
    JobType["MECHANIC"] = "mechanic";
    JobType["TAXI"] = "taxi";
    JobType["TRUCKER"] = "trucker";
    JobType["DELIVERY"] = "delivery";
    JobType["GARBAGE"] = "garbage";
    JobType["MINER"] = "miner";
    JobType["LUMBERJACK"] = "lumberjack";
    JobType["FISHERMAN"] = "fisherman";
    JobType["FARMER"] = "farmer";
    JobType["CHEF"] = "chef";
    JobType["BARTENDER"] = "bartender";
    JobType["SHOP_KEEPER"] = "shopkeeper";
    JobType["BANK_TELLER"] = "bankteller";
    JobType["LAWYER"] = "lawyer";
    JobType["JUDGE"] = "judge";
    JobType["REAL_ESTATE"] = "realestate";
    JobType["NEWS_REPORTER"] = "newsreporter";
})(JobType || (exports.JobType = JobType = {}));
// Property types
var PropertyType;
(function (PropertyType) {
    PropertyType["HOUSE"] = "house";
    PropertyType["APARTMENT"] = "apartment";
    PropertyType["BUSINESS"] = "business";
    PropertyType["GARAGE"] = "garage";
    PropertyType["WAREHOUSE"] = "warehouse";
})(PropertyType || (exports.PropertyType = PropertyType = {}));
// Inventory item types
var ItemType;
(function (ItemType) {
    ItemType["WEAPON"] = "weapon";
    ItemType["AMMO"] = "ammo";
    ItemType["FOOD"] = "food";
    ItemType["DRINK"] = "drink";
    ItemType["MEDICAL"] = "medical";
    ItemType["TOOL"] = "tool";
    ItemType["CLOTHING"] = "clothing";
    ItemType["ACCESSORY"] = "accessory";
    ItemType["DOCUMENT"] = "document";
    ItemType["KEY"] = "key";
    ItemType["PHONE"] = "phone";
    ItemType["ELECTRONICS"] = "electronics";
    ItemType["DRUG"] = "drug";
    ItemType["MISC"] = "misc";
})(ItemType || (exports.ItemType = ItemType = {}));
// Chat types
var ChatType;
(function (ChatType) {
    ChatType["LOCAL"] = "local";
    ChatType["WHISPER"] = "whisper";
    ChatType["SHOUT"] = "shout";
    ChatType["ME"] = "me";
    ChatType["DO"] = "do";
    ChatType["OOC"] = "ooc";
    ChatType["GLOBAL"] = "global";
    ChatType["ADMIN"] = "admin";
    ChatType["FACTION"] = "faction";
    ChatType["JOB"] = "job";
    ChatType["PHONE"] = "phone";
    ChatType["RADIO"] = "radio";
})(ChatType || (exports.ChatType = ChatType = {}));
// Admin levels
var AdminLevel;
(function (AdminLevel) {
    AdminLevel[AdminLevel["PLAYER"] = 0] = "PLAYER";
    AdminLevel[AdminLevel["HELPER"] = 1] = "HELPER";
    AdminLevel[AdminLevel["MODERATOR"] = 2] = "MODERATOR";
    AdminLevel[AdminLevel["ADMIN"] = 3] = "ADMIN";
    AdminLevel[AdminLevel["SENIOR_ADMIN"] = 4] = "SENIOR_ADMIN";
    AdminLevel[AdminLevel["HEAD_ADMIN"] = 5] = "HEAD_ADMIN";
    AdminLevel[AdminLevel["DEVELOPER"] = 6] = "DEVELOPER";
    AdminLevel[AdminLevel["OWNER"] = 7] = "OWNER";
})(AdminLevel || (exports.AdminLevel = AdminLevel = {}));
// Notification types
var NotificationType;
(function (NotificationType) {
    NotificationType["SUCCESS"] = "success";
    NotificationType["ERROR"] = "error";
    NotificationType["WARNING"] = "warning";
    NotificationType["INFO"] = "info";
})(NotificationType || (exports.NotificationType = NotificationType = {}));
// UI types
var UIType;
(function (UIType) {
    UIType["LOGIN"] = "login";
    UIType["REGISTER"] = "register";
    UIType["CHARACTER_CREATOR"] = "characterCreator";
    UIType["CHARACTER_SELECTOR"] = "characterSelector";
    UIType["INVENTORY"] = "inventory";
    UIType["PHONE"] = "phone";
    UIType["ATM"] = "atm";
    UIType["VEHICLE_HUD"] = "vehicleHud";
    UIType["SPEEDOMETER"] = "speedometer";
    UIType["CHAT"] = "chat";
    UIType["ADMIN_PANEL"] = "adminPanel";
    UIType["JOB_MENU"] = "jobMenu";
    UIType["SHOP"] = "shop";
    UIType["GARAGE"] = "garage";
    UIType["PROPERTY_MENU"] = "propertyMenu";
})(UIType || (exports.UIType = UIType = {}));
// Interaction types
var InteractionType;
(function (InteractionType) {
    InteractionType["DOOR"] = "door";
    InteractionType["VEHICLE"] = "vehicle";
    InteractionType["ATM"] = "atm";
    InteractionType["SHOP"] = "shop";
    InteractionType["JOB_POINT"] = "jobPoint";
    InteractionType["PROPERTY"] = "property";
    InteractionType["NPC"] = "npc";
    InteractionType["OBJECT"] = "object";
})(InteractionType || (exports.InteractionType = InteractionType = {}));
// Weather types
var WeatherType;
(function (WeatherType) {
    WeatherType["CLEAR"] = "clear";
    WeatherType["CLOUDY"] = "cloudy";
    WeatherType["OVERCAST"] = "overcast";
    WeatherType["RAIN"] = "rain";
    WeatherType["THUNDER"] = "thunder";
    WeatherType["FOG"] = "fog";
    WeatherType["SNOW"] = "snow";
    WeatherType["BLIZZARD"] = "blizzard";
})(WeatherType || (exports.WeatherType = WeatherType = {}));
// Time periods
var TimePeriod;
(function (TimePeriod) {
    TimePeriod["MORNING"] = "morning";
    TimePeriod["AFTERNOON"] = "afternoon";
    TimePeriod["EVENING"] = "evening";
    TimePeriod["NIGHT"] = "night";
})(TimePeriod || (exports.TimePeriod = TimePeriod = {}));
// Faction types
var FactionType;
(function (FactionType) {
    FactionType["GANG"] = "gang";
    FactionType["MAFIA"] = "mafia";
    FactionType["MC"] = "mc";
    FactionType["CARTEL"] = "cartel";
    FactionType["GOVERNMENT"] = "government";
    FactionType["BUSINESS"] = "business";
})(FactionType || (exports.FactionType = FactionType = {}));
// Vehicle classes
var VehicleClass;
(function (VehicleClass) {
    VehicleClass[VehicleClass["COMPACT"] = 0] = "COMPACT";
    VehicleClass[VehicleClass["SEDAN"] = 1] = "SEDAN";
    VehicleClass[VehicleClass["SUV"] = 2] = "SUV";
    VehicleClass[VehicleClass["COUPE"] = 3] = "COUPE";
    VehicleClass[VehicleClass["MUSCLE"] = 4] = "MUSCLE";
    VehicleClass[VehicleClass["SPORTS_CLASSIC"] = 5] = "SPORTS_CLASSIC";
    VehicleClass[VehicleClass["SPORTS"] = 6] = "SPORTS";
    VehicleClass[VehicleClass["SUPER"] = 7] = "SUPER";
    VehicleClass[VehicleClass["MOTORCYCLE"] = 8] = "MOTORCYCLE";
    VehicleClass[VehicleClass["OFF_ROAD"] = 9] = "OFF_ROAD";
    VehicleClass[VehicleClass["INDUSTRIAL"] = 10] = "INDUSTRIAL";
    VehicleClass[VehicleClass["UTILITY"] = 11] = "UTILITY";
    VehicleClass[VehicleClass["VAN"] = 12] = "VAN";
    VehicleClass[VehicleClass["CYCLE"] = 13] = "CYCLE";
    VehicleClass[VehicleClass["BOAT"] = 14] = "BOAT";
    VehicleClass[VehicleClass["HELICOPTER"] = 15] = "HELICOPTER";
    VehicleClass[VehicleClass["PLANE"] = 16] = "PLANE";
    VehicleClass[VehicleClass["SERVICE"] = 17] = "SERVICE";
    VehicleClass[VehicleClass["EMERGENCY"] = 18] = "EMERGENCY";
    VehicleClass[VehicleClass["MILITARY"] = 19] = "MILITARY";
    VehicleClass[VehicleClass["COMMERCIAL"] = 20] = "COMMERCIAL";
    VehicleClass[VehicleClass["TRAIN"] = 21] = "TRAIN";
})(VehicleClass || (exports.VehicleClass = VehicleClass = {}));
//# sourceMappingURL=index.js.map
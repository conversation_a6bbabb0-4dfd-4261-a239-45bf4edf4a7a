"use strict";
/**
 * RageMP Roleplay Server
 * Main entry point for the server-side application
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.Server = void 0;
const dotenv_1 = require("dotenv");
const DatabaseManager_1 = require("./database/DatabaseManager");
const EventManager_1 = require("./events/EventManager");
const AuthModule_1 = require("./modules/auth/AuthModule");
const CharacterModule_1 = require("./modules/character/CharacterModule");
const EconomyModule_1 = require("./modules/economy/EconomyModule");
const InventoryModule_1 = require("./modules/inventory/InventoryModule");
const JobsModule_1 = require("./modules/jobs/JobsModule");
const HousingModule_1 = require("./modules/housing/HousingModule");
const Logger_1 = require("./utils/Logger");
// Load environment variables
(0, dotenv_1.config)();
class RoleplayServer {
    static instance;
    modules = new Map();
    isInitialized = false;
    constructor() {
        if (RoleplayServer.instance) {
            return RoleplayServer.instance;
        }
        RoleplayServer.instance = this;
    }
    /**
     * Initialize the server
     */
    async initialize() {
        try {
            Logger_1.Logger.info('🚀 Starting RageMP Roleplay Server...');
            // Initialize database connection
            await this.initializeDatabase();
            // Initialize event manager
            this.initializeEventManager();
            // Initialize modules
            await this.initializeModules();
            // Setup global event handlers
            this.setupGlobalEvents();
            this.isInitialized = true;
            Logger_1.Logger.success('✅ RageMP Roleplay Server started successfully!');
        }
        catch (error) {
            Logger_1.Logger.error('❌ Failed to start server:', error);
            throw error;
        }
    }
    /**
     * Initialize database connection
     */
    async initializeDatabase() {
        Logger_1.Logger.info('📊 Initializing database connection...');
        await DatabaseManager_1.DatabaseManager.getInstance().connect();
        Logger_1.Logger.success('✅ Database connected successfully');
    }
    /**
     * Initialize event manager
     */
    initializeEventManager() {
        Logger_1.Logger.info('📡 Initializing event manager...');
        EventManager_1.EventManager.getInstance().initialize();
        Logger_1.Logger.success('✅ Event manager initialized');
    }
    /**
     * Initialize all modules
     */
    async initializeModules() {
        Logger_1.Logger.info('🔧 Initializing modules...');
        const moduleClasses = [
            { name: 'auth', class: AuthModule_1.AuthModule },
            { name: 'character', class: CharacterModule_1.CharacterModule },
            { name: 'economy', class: EconomyModule_1.EconomyModule },
            { name: 'inventory', class: InventoryModule_1.InventoryModule },
            { name: 'jobs', class: JobsModule_1.JobsModule },
            { name: 'housing', class: HousingModule_1.HousingModule }
        ];
        for (const { name, class: ModuleClass } of moduleClasses) {
            try {
                Logger_1.Logger.info(`  📦 Loading ${name} module...`);
                const moduleInstance = new ModuleClass();
                await moduleInstance.initialize();
                this.modules.set(name, moduleInstance);
                Logger_1.Logger.success(`  ✅ ${name} module loaded`);
            }
            catch (error) {
                Logger_1.Logger.error(`  ❌ Failed to load ${name} module:`, error);
                throw error;
            }
        }
        Logger_1.Logger.success('✅ All modules initialized successfully');
    }
    /**
     * Setup global event handlers
     */
    setupGlobalEvents() {
        Logger_1.Logger.info('🎯 Setting up global event handlers...');
        // Player join event
        mp.events.add('playerJoin', (player) => {
            Logger_1.Logger.info(`👤 Player ${player.name} (${player.socialClub}) joined the server`);
            // Initialize player data
            player.dimension = 0;
            player.alpha = 255;
            // Trigger auth module
            const authModule = this.modules.get('auth');
            if (authModule) {
                authModule.onPlayerJoin(player);
            }
        });
        // Player quit event
        mp.events.add('playerQuit', (player, exitType, reason) => {
            Logger_1.Logger.info(`👤 Player ${player.name} left the server (${exitType}: ${reason})`);
            // Cleanup player data
            const characterModule = this.modules.get('character');
            if (characterModule) {
                characterModule.onPlayerQuit(player);
            }
        });
        // Player death event
        mp.events.add('playerDeath', (player, reason, killer) => {
            Logger_1.Logger.info(`💀 Player ${player.name} died (reason: ${reason})`);
            // Handle player death
            const characterModule = this.modules.get('character');
            if (characterModule) {
                characterModule.onPlayerDeath(player, reason, killer);
            }
        });
        // Player chat event
        mp.events.add('playerChat', (player, text) => {
            // Handle chat through character module
            const characterModule = this.modules.get('character');
            if (characterModule) {
                characterModule.onPlayerChat(player, text);
            }
        });
        Logger_1.Logger.success('✅ Global event handlers setup complete');
    }
    /**
     * Get module instance
     */
    getModule(name) {
        return this.modules.get(name);
    }
    /**
     * Check if server is initialized
     */
    get initialized() {
        return this.isInitialized;
    }
    /**
     * Shutdown the server gracefully
     */
    async shutdown() {
        Logger_1.Logger.info('🛑 Shutting down server...');
        // Shutdown modules
        for (const [name, module] of this.modules) {
            try {
                if (module.shutdown) {
                    await module.shutdown();
                    Logger_1.Logger.info(`  ✅ ${name} module shutdown complete`);
                }
            }
            catch (error) {
                Logger_1.Logger.error(`  ❌ Error shutting down ${name} module:`, error);
            }
        }
        // Close database connection
        await DatabaseManager_1.DatabaseManager.getInstance().disconnect();
        Logger_1.Logger.success('✅ Server shutdown complete');
    }
}
// Global server instance
let serverInstance;
// Initialize server when script loads
(async () => {
    try {
        exports.Server = serverInstance = new RoleplayServer();
        await serverInstance.initialize();
    }
    catch (error) {
        Logger_1.Logger.error('Failed to initialize server:', error);
        process.exit(1);
    }
})();
// Handle process termination
process.on('SIGINT', async () => {
    if (serverInstance) {
        await serverInstance.shutdown();
    }
    process.exit(0);
});
process.on('SIGTERM', async () => {
    if (serverInstance) {
        await serverInstance.shutdown();
    }
    process.exit(0);
});
//# sourceMappingURL=index.js.map
/**
 * Client-side Character Manager
 */

export class CharacterManager {
  private activeCharacter: any = null;
  private characters: any[] = [];

  /**
   * Initialize character manager
   */
  public async initialize(): Promise<void> {
    console.log('👤 Initializing Character Manager...');
    this.setupCharacterEvents();
  }

  /**
   * Setup character events
   */
  private setupCharacterEvents(): void {
    // Character data from server
    mp.events.add('character:data', (character: any) => {
      this.setActiveCharacter(character);
    });

    // Character list from server
    mp.events.add('character:list', (characters: any[]) => {
      this.setCharacters(characters);
    });

    // Character update from server
    mp.events.add('character:update', (updates: any) => {
      this.updateCharacter(updates);
    });

    console.log('✅ Character events setup complete');
  }

  /**
   * Set active character
   */
  private setActiveCharacter(character: any): void {
    this.activeCharacter = character;
    console.log('👤 Active character set:', character.firstName, character.lastName);
    
    // Hide character selection UI and show HUD
    mp.events.call('ui:hide');
  }

  /**
   * Set characters list
   */
  private setCharacters(characters: any[]): void {
    this.characters = characters;
    console.log('👥 Characters loaded:', characters.length);
  }

  /**
   * Update character data
   */
  private updateCharacter(updates: any): void {
    if (this.activeCharacter) {
      Object.assign(this.activeCharacter, updates);
      console.log('👤 Character updated');
    }
  }

  /**
   * Handle player death
   */
  public onPlayerDeath(reason: number, killer: PlayerMp): void {
    console.log('💀 Player died, reason:', reason);
    
    // Handle death logic here
    // Show death screen, respawn options, etc.
  }

  /**
   * Handle player chat
   */
  public onPlayerChat(player: PlayerMp, text: string): void {
    // Handle chat through server
    mp.events.callRemote('chat:sendMessage', {
      type: 'local',
      message: text
    });
  }

  /**
   * Get active character
   */
  public get character(): any {
    return this.activeCharacter;
  }

  /**
   * Get characters list
   */
  public get charactersList(): any[] {
    return this.characters;
  }

  /**
   * Shutdown character manager
   */
  public async shutdown(): Promise<void> {
    console.log('🛑 Shutting down Character Manager...');
    this.activeCharacter = null;
    this.characters = [];
  }
}

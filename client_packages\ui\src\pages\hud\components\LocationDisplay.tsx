import { useState, useEffect } from 'react'
import { useSelector } from 'react-redux'
import { motion } from 'framer-motion'
import { MapPin, Navigation } from 'lucide-react'
import { RootState } from '@/store'

const LocationDisplay = () => {
  const { showLocation } = useSelector((state: RootState) => state.hud)
  const { activeCharacter } = useSelector((state: RootState) => state.character)
  const [streetName, setStreetName] = useState('Unknown Location')
  const [zone, setZone] = useState('Los Santos')

  useEffect(() => {
    // In a real implementation, this would get location data from RageMP
    // For now, we'll simulate it
    const updateLocation = () => {
      if (activeCharacter?.position) {
        // This would be replaced with actual street/zone lookup
        setStreetName('Grove Street')
        setZone('Ganton')
      }
    }

    updateLocation()
    const interval = setInterval(updateLocation, 2000)

    return () => clearInterval(interval)
  }, [activeCharacter?.position])

  if (!showLocation || !activeCharacter) return null

  return (
    <motion.div
      initial={{ opacity: 0, y: 50 }}
      animate={{ opacity: 1, y: 0 }}
      className="glass-card p-3 min-w-[250px]"
    >
      <div className="space-y-2">
        {/* Street Name */}
        <div className="flex items-center space-x-3">
          <Navigation className="w-4 h-4 text-green-400" />
          <div className="flex-1">
            <div className="text-white font-bold text-lg">
              {streetName}
            </div>
          </div>
        </div>

        {/* Zone */}
        <div className="flex items-center space-x-3">
          <MapPin className="w-4 h-4 text-gray-400" />
          <div className="flex-1">
            <div className="text-gray-300 text-sm">
              {zone}
            </div>
          </div>
        </div>

        {/* Coordinates */}
        {activeCharacter.position && (
          <div className="text-xs text-gray-400 font-mono">
            X: {activeCharacter.position.x.toFixed(1)} | 
            Y: {activeCharacter.position.y.toFixed(1)} | 
            Z: {activeCharacter.position.z.toFixed(1)}
          </div>
        )}
      </div>
    </motion.div>
  )
}

export default LocationDisplay

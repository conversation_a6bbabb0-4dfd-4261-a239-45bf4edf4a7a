{"version": 3, "file": "EventManager.js", "sourceRoot": "", "sources": ["../../src/events/EventManager.ts"], "names": [], "mappings": ";AAAA;;GAEG;;;AAEH,4CAAyC;AACzC,8CAI2B;AAE3B,MAAa,YAAY;IACf,MAAM,CAAC,QAAQ,CAAe;IAC9B,aAAa,GAA4B,IAAI,GAAG,EAAE,CAAC;IAE3D,gBAAuB,CAAC;IAExB;;OAEG;IACI,MAAM,CAAC,WAAW;QACvB,IAAI,CAAC,YAAY,CAAC,QAAQ,EAAE,CAAC;YAC3B,YAAY,CAAC,QAAQ,GAAG,IAAI,YAAY,EAAE,CAAC;QAC7C,CAAC;QACD,OAAO,YAAY,CAAC,QAAQ,CAAC;IAC/B,CAAC;IAED;;OAEG;IACI,UAAU;QACf,eAAM,CAAC,IAAI,CAAC,+BAA+B,CAAC,CAAC;QAC7C,IAAI,CAAC,kBAAkB,EAAE,CAAC;IAC5B,CAAC;IAED;;OAEG;IACK,kBAAkB;QACxB,mDAAmD;QACnD,eAAM,CAAC,KAAK,CAAC,+BAA+B,CAAC,CAAC;IAChD,CAAC;IAED;;OAEG;IACI,EAAE,CAAC,SAAiB,EAAE,OAAiB;QAC5C,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,SAAS,CAAC,EAAE,CAAC;YACvC,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,SAAS,EAAE,EAAE,CAAC,CAAC;QACxC,CAAC;QAED,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,SAAS,CAAE,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QAEjD,wDAAwD;QACxD,IAAI,MAAM,CAAC,MAAM,CAAC,6BAAoB,CAAC,CAAC,QAAQ,CAAC,SAAgB,CAAC,EAAE,CAAC;YACnE,EAAE,CAAC,MAAM,CAAC,GAAG,CAAC,SAAS,EAAE,OAAO,CAAC,CAAC;QACpC,CAAC;QAED,eAAM,CAAC,KAAK,CAAC,iCAAiC,SAAS,EAAE,CAAC,CAAC;IAC7D,CAAC;IAED;;OAEG;IACI,GAAG,CAAC,SAAiB,EAAE,OAAkB;QAC9C,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,SAAS,CAAC,EAAE,CAAC;YACvC,OAAO;QACT,CAAC;QAED,IAAI,OAAO,EAAE,CAAC;YACZ,MAAM,QAAQ,GAAG,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,SAAS,CAAE,CAAC;YACpD,MAAM,KAAK,GAAG,QAAQ,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;YACxC,IAAI,KAAK,GAAG,CAAC,CAAC,EAAE,CAAC;gBACf,QAAQ,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;gBAC1B,EAAE,CAAC,MAAM,CAAC,MAAM,CAAC,SAAS,EAAE,OAAO,CAAC,CAAC;YACvC,CAAC;QACH,CAAC;aAAM,CAAC;YACN,qCAAqC;YACrC,MAAM,QAAQ,GAAG,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,SAAS,CAAE,CAAC;YACpD,QAAQ,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,MAAM,CAAC,MAAM,CAAC,SAAS,EAAE,CAAC,CAAC,CAAC,CAAC;YACtD,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;QACvC,CAAC;QAED,eAAM,CAAC,KAAK,CAAC,sCAAsC,SAAS,EAAE,CAAC,CAAC;IAClE,CAAC;IAED;;OAEG;IACI,UAAU,CACf,MAA6B,EAC7B,SAAwC,EACxC,GAAG,IAAW;QAEd,IAAI,CAAC;YACH,IAAI,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE,CAAC;gBAC1B,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE;oBACjB,IAAI,CAAC,IAAI,CAAC,CAAC,IAAI,EAAE,CAAC;wBAChB,CAAC,CAAC,IAAI,CAAC,SAAS,EAAE,GAAG,IAAI,CAAC,CAAC;oBAC7B,CAAC;gBACH,CAAC,CAAC,CAAC;YACL,CAAC;iBAAM,CAAC;gBACN,IAAI,MAAM,IAAI,MAAM,CAAC,IAAI,EAAE,CAAC;oBAC1B,MAAM,CAAC,IAAI,CAAC,SAAS,EAAE,GAAG,IAAI,CAAC,CAAC;gBAClC,CAAC;YACH,CAAC;YAED,eAAM,CAAC,KAAK,CAAC,iBAAiB,SAAS,eAAe,CAAC,CAAC;QAC1D,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,wBAAwB,SAAS,GAAG,EAAE,KAAK,CAAC,CAAC;QAC5D,CAAC;IACH,CAAC;IAED;;OAEG;IACI,cAAc,CAAC,SAAwC,EAAE,GAAG,IAAW;QAC5E,IAAI,CAAC;YACH,EAAE,CAAC,OAAO,CAAC,IAAI,CAAC,SAAS,EAAE,GAAG,IAAI,CAAC,CAAC;YACpC,eAAM,CAAC,KAAK,CAAC,iBAAiB,SAAS,iBAAiB,CAAC,CAAC;QAC5D,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,wBAAwB,SAAS,kBAAkB,EAAE,KAAK,CAAC,CAAC;QAC3E,CAAC;IACH,CAAC;IAED;;OAEG;IACI,kBAAkB,CACvB,QAAmB,EACnB,KAAa,EACb,SAAiB,EACjB,SAAwC,EACxC,GAAG,IAAW;QAEd,IAAI,CAAC;YACH,MAAM,cAAc,GAAG,EAAE,CAAC,OAAO,CAAC,OAAO,EAAE,CAAC,MAAM,CAAC,MAAM,CAAC,EAAE;gBAC1D,IAAI,MAAM,CAAC,SAAS,KAAK,SAAS;oBAAE,OAAO,KAAK,CAAC;gBAEjD,MAAM,QAAQ,GAAG,MAAM,CAAC,QAAQ,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC;gBACtD,OAAO,QAAQ,IAAI,KAAK,CAAC;YAC3B,CAAC,CAAC,CAAC;YAEH,IAAI,CAAC,UAAU,CAAC,cAAc,EAAE,SAAS,EAAE,GAAG,IAAI,CAAC,CAAC;YACpD,eAAM,CAAC,KAAK,CAAC,iBAAiB,SAAS,OAAO,cAAc,CAAC,MAAM,mBAAmB,CAAC,CAAC;QAC1F,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,wBAAwB,SAAS,uBAAuB,EAAE,KAAK,CAAC,CAAC;QAChF,CAAC;IACH,CAAC;IAED;;OAEG;IACI,WAAW,CAChB,MAAgB,EAChB,SAAiB,EACjB,IAAiB;QAEjB,IAAI,CAAC;YACH,IAAI,MAAM,IAAI,MAAM,CAAC,IAAI,EAAE,CAAC;gBAC1B,MAAM,CAAC,IAAI,CAAC,UAAU,EAAE,CAAC,SAAS,EAAE,IAAI,CAAC,CAAC,CAAC;YAC7C,CAAC;YACD,eAAM,CAAC,KAAK,CAAC,yBAAyB,SAAS,cAAc,MAAM,CAAC,IAAI,EAAE,CAAC,CAAC;QAC9E,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,gCAAgC,SAAS,GAAG,EAAE,KAAK,CAAC,CAAC;QACpE,CAAC;IACH,CAAC;IAED;;OAEG;IACI,KAAK,CAAC,UAAU,CACrB,MAAgB,EAChB,SAAiB,EACjB,GAAG,IAAW;QAEd,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;YACrC,IAAI,CAAC;gBACH,MAAM,OAAO,GAAG,UAAU,CAAC,GAAG,EAAE;oBAC9B,MAAM,CAAC,IAAI,KAAK,CAAC,YAAY,SAAS,YAAY,CAAC,CAAC,CAAC;gBACvD,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC,oBAAoB;gBAE/B,MAAM,MAAM,GAAG,IAAI,CAAC,cAAc,EAAE,CAAC;gBACrC,MAAM,aAAa,GAAG,gBAAgB,MAAM,EAAE,CAAC;gBAE/C,kCAAkC;gBAClC,MAAM,eAAe,GAAG,CAAC,cAAwB,EAAE,MAAS,EAAE,KAAc,EAAE,EAAE;oBAC9E,IAAI,cAAc,KAAK,MAAM,EAAE,CAAC;wBAC9B,YAAY,CAAC,OAAO,CAAC,CAAC;wBACtB,EAAE,CAAC,MAAM,CAAC,MAAM,CAAC,aAAa,EAAE,eAAe,CAAC,CAAC;wBAEjD,IAAI,KAAK,EAAE,CAAC;4BACV,MAAM,CAAC,IAAI,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC;wBAC3B,CAAC;6BAAM,CAAC;4BACN,OAAO,CAAC,MAAM,CAAC,CAAC;wBAClB,CAAC;oBACH,CAAC;gBACH,CAAC,CAAC;gBAEF,EAAE,CAAC,MAAM,CAAC,GAAG,CAAC,aAAa,EAAE,eAAe,CAAC,CAAC;gBAC9C,MAAM,CAAC,IAAI,CAAC,UAAU,EAAE,CAAC,SAAS,EAAE,MAAM,EAAE,GAAG,IAAI,CAAC,CAAC,CAAC;YAExD,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,MAAM,CAAC,KAAK,CAAC,CAAC;YAChB,CAAC;QACH,CAAC,CAAC,CAAC;IACL,CAAC;IAED;;OAEG;IACK,cAAc;QACpB,OAAO,GAAG,IAAI,CAAC,GAAG,EAAE,IAAI,IAAI,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC;IACpE,CAAC;IAED;;OAEG;IACI,WAAW,CAAC,SAAiB;QAClC,OAAO,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,SAAS,CAAC,IAAI,EAAE,CAAC;IACjD,CAAC;IAED;;OAEG;IACI,WAAW,CAAC,SAAiB;QAClC,OAAO,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,SAAS,CAAC,IAAI,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,SAAS,CAAE,CAAC,MAAM,GAAG,CAAC,CAAC;IAC5F,CAAC;IAED;;OAEG;IACI,mBAAmB;QACxB,OAAO,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,IAAI,EAAE,CAAC,CAAC;IAC/C,CAAC;CACF;AAhOD,oCAgOC"}
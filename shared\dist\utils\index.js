"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ArrayUtils = exports.ColorUtils = exports.ValidationUtils = exports.TimeUtils = exports.StringUtils = exports.MathUtils = void 0;
// Math utilities
class MathUtils {
    /**
     * Calculate distance between two 3D points
     */
    static distance3D(pos1, pos2) {
        const dx = pos1.x - pos2.x;
        const dy = pos1.y - pos2.y;
        const dz = pos1.z - pos2.z;
        return Math.sqrt(dx * dx + dy * dy + dz * dz);
    }
    /**
     * Calculate distance between two 2D points (ignoring Z axis)
     */
    static distance2D(pos1, pos2) {
        const dx = pos1.x - pos2.x;
        const dy = pos1.y - pos2.y;
        return Math.sqrt(dx * dx + dy * dy);
    }
    /**
     * Generate random number between min and max
     */
    static randomBetween(min, max) {
        return Math.random() * (max - min) + min;
    }
    /**
     * Generate random integer between min and max (inclusive)
     */
    static randomIntBetween(min, max) {
        return Math.floor(Math.random() * (max - min + 1)) + min;
    }
    /**
     * Clamp value between min and max
     */
    static clamp(value, min, max) {
        return Math.min(Math.max(value, min), max);
    }
    /**
     * Linear interpolation
     */
    static lerp(start, end, factor) {
        return start + (end - start) * factor;
    }
    /**
     * Convert degrees to radians
     */
    static degToRad(degrees) {
        return degrees * (Math.PI / 180);
    }
    /**
     * Convert radians to degrees
     */
    static radToDeg(radians) {
        return radians * (180 / Math.PI);
    }
}
exports.MathUtils = MathUtils;
// String utilities
class StringUtils {
    /**
     * Capitalize first letter of string
     */
    static capitalize(str) {
        return str.charAt(0).toUpperCase() + str.slice(1).toLowerCase();
    }
    /**
     * Convert string to title case
     */
    static toTitleCase(str) {
        return str.replace(/\w\S*/g, (txt) => txt.charAt(0).toUpperCase() + txt.substr(1).toLowerCase());
    }
    /**
     * Generate random string of specified length
     */
    static randomString(length, chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789') {
        let result = '';
        for (let i = 0; i < length; i++) {
            result += chars.charAt(Math.floor(Math.random() * chars.length));
        }
        return result;
    }
    /**
     * Generate UUID v4
     */
    static generateUUID() {
        return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function (c) {
            const r = Math.random() * 16 | 0;
            const v = c === 'x' ? r : (r & 0x3 | 0x8);
            return v.toString(16);
        });
    }
    /**
     * Sanitize string for database/security
     */
    static sanitize(str) {
        return str.replace(/[<>\"'%;()&+]/g, '');
    }
    /**
     * Format number with commas
     */
    static formatNumber(num) {
        return num.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',');
    }
    /**
     * Format currency
     */
    static formatCurrency(amount, symbol = '$') {
        return `${symbol}${this.formatNumber(amount)}`;
    }
}
exports.StringUtils = StringUtils;
// Time utilities
class TimeUtils {
    /**
     * Format timestamp to readable string
     */
    static formatTimestamp(timestamp) {
        const date = timestamp instanceof Date ? timestamp : new Date(timestamp);
        return date.toLocaleString();
    }
    /**
     * Get time ago string
     */
    static timeAgo(timestamp) {
        const now = new Date().getTime();
        const time = timestamp instanceof Date ? timestamp.getTime() : timestamp;
        const diff = now - time;
        const seconds = Math.floor(diff / 1000);
        const minutes = Math.floor(seconds / 60);
        const hours = Math.floor(minutes / 60);
        const days = Math.floor(hours / 24);
        if (days > 0)
            return `${days} day${days > 1 ? 's' : ''} ago`;
        if (hours > 0)
            return `${hours} hour${hours > 1 ? 's' : ''} ago`;
        if (minutes > 0)
            return `${minutes} minute${minutes > 1 ? 's' : ''} ago`;
        return `${seconds} second${seconds > 1 ? 's' : ''} ago`;
    }
    /**
     * Format duration in milliseconds to readable string
     */
    static formatDuration(ms) {
        const seconds = Math.floor(ms / 1000);
        const minutes = Math.floor(seconds / 60);
        const hours = Math.floor(minutes / 60);
        const days = Math.floor(hours / 24);
        if (days > 0)
            return `${days}d ${hours % 24}h ${minutes % 60}m`;
        if (hours > 0)
            return `${hours}h ${minutes % 60}m ${seconds % 60}s`;
        if (minutes > 0)
            return `${minutes}m ${seconds % 60}s`;
        return `${seconds}s`;
    }
}
exports.TimeUtils = TimeUtils;
// Validation utilities
class ValidationUtils {
    /**
     * Validate email format
     */
    static isValidEmail(email) {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return emailRegex.test(email);
    }
    /**
     * Validate username format
     */
    static isValidUsername(username) {
        const usernameRegex = /^[a-zA-Z0-9_]{3,20}$/;
        return usernameRegex.test(username);
    }
    /**
     * Validate password strength
     */
    static isValidPassword(password) {
        // At least 8 characters, 1 uppercase, 1 lowercase, 1 number
        const passwordRegex = /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)[a-zA-Z\d@$!%*?&]{8,}$/;
        return passwordRegex.test(password);
    }
    /**
     * Validate character name
     */
    static isValidCharacterName(name) {
        const nameRegex = /^[a-zA-Z]{2,15}$/;
        return nameRegex.test(name);
    }
    /**
     * Check if string contains only numbers
     */
    static isNumeric(str) {
        return /^\d+$/.test(str);
    }
    /**
     * Check if value is within range
     */
    static isInRange(value, min, max) {
        return value >= min && value <= max;
    }
}
exports.ValidationUtils = ValidationUtils;
// Color utilities
class ColorUtils {
    /**
     * Convert RGB to hex
     */
    static rgbToHex(r, g, b) {
        return `#${((1 << 24) + (r << 16) + (g << 8) + b).toString(16).slice(1)}`;
    }
    /**
     * Convert hex to RGB
     */
    static hexToRgb(hex) {
        const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
        return result ? {
            r: parseInt(result[1], 16),
            g: parseInt(result[2], 16),
            b: parseInt(result[3], 16)
        } : null;
    }
    /**
     * Generate random color
     */
    static randomColor() {
        return `#${Math.floor(Math.random() * 16777215).toString(16)}`;
    }
}
exports.ColorUtils = ColorUtils;
// Array utilities
class ArrayUtils {
    /**
     * Get random element from array
     */
    static randomElement(array) {
        return array[Math.floor(Math.random() * array.length)];
    }
    /**
     * Shuffle array
     */
    static shuffle(array) {
        const shuffled = [...array];
        for (let i = shuffled.length - 1; i > 0; i--) {
            const j = Math.floor(Math.random() * (i + 1));
            [shuffled[i], shuffled[j]] = [shuffled[j], shuffled[i]];
        }
        return shuffled;
    }
    /**
     * Remove duplicates from array
     */
    static unique(array) {
        return [...new Set(array)];
    }
    /**
     * Chunk array into smaller arrays
     */
    static chunk(array, size) {
        const chunks = [];
        for (let i = 0; i < array.length; i += size) {
            chunks.push(array.slice(i, i + size));
        }
        return chunks;
    }
}
exports.ArrayUtils = ArrayUtils;
//# sourceMappingURL=index.js.map
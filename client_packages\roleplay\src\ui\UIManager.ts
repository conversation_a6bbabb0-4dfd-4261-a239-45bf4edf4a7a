/**
 * UI Manager for handling CEF browsers and UI interactions
 */

export class UIManager {
  private static instance: UIManager;
  private browsers: Map<string, BrowserMp> = new Map();
  private activeBrowser: BrowserMp | null = null;

  private constructor() {}

  /**
   * Get singleton instance
   */
  public static getInstance(): UIManager {
    if (!UIManager.instance) {
      UIManager.instance = new UIManager();
    }
    return UIManager.instance;
  }

  /**
   * Initialize UI manager
   */
  public async initialize(): Promise<void> {
    console.log('🖥️ Initializing UI Manager...');
    this.setupUIEvents();
  }

  /**
   * Setup UI-related events
   */
  private setupUIEvents(): void {
    // Show UI event from server
    mp.events.add('ui:show', (data: any) => {
      this.showUI(data.type, data.data);
    });

    // Hide UI event from server
    mp.events.add('ui:hide', () => {
      this.hideUI();
    });

    // Update UI data event from server
    mp.events.add('ui:updateData', (data: any) => {
      this.updateUIData(data);
    });

    console.log('✅ UI events setup complete');
  }

  /**
   * Create or get browser instance
   */
  private getBrowser(name: string): BrowserMp {
    if (!this.browsers.has(name)) {
      const browser = (mp.browsers as any).new('package://ui/dist/index.html');
      this.browsers.set(name, browser);
      
      // Setup browser events
      browser.execute(`
        window.mp = window.mp || {};
        window.mp.trigger = function(eventName, ...args) {
          if (window.mp.events && window.mp.events.call) {
            window.mp.events.call('browserToClient', eventName, ...args);
          }
        };
      `);
    }
    
    return this.browsers.get(name)!;
  }

  /**
   * Show UI
   */
  public showUI(type: string, data?: any): void {
    console.log(`🖥️ Showing UI: ${type}`);
    
    const browser = this.getBrowser('main');
    this.activeBrowser = browser;
    
    // Show cursor and disable game controls
    mp.gui.cursor.show(true, true);
    mp.game.ui.displayRadar(false);
    
    // Send show event to React app
    browser.execute(`
      if (window.mp && window.mp.events) {
        window.mp.events.add('ui:show', ${JSON.stringify({ type, data })});
      }
    `);
  }

  /**
   * Hide UI
   */
  public hideUI(): void {
    console.log('🖥️ Hiding UI');
    
    if (this.activeBrowser) {
      // Hide cursor and enable game controls
      mp.gui.cursor.show(false, false);
      mp.game.ui.displayRadar(true);
      
      // Send hide event to React app
      this.activeBrowser.execute(`
        if (window.mp && window.mp.events) {
          window.mp.events.add('ui:hide', {});
        }
      `);
      
      this.activeBrowser = null;
    }
  }

  /**
   * Update UI data
   */
  public updateUIData(data: any): void {
    if (this.activeBrowser) {
      this.activeBrowser.execute(`
        if (window.mp && window.mp.events) {
          window.mp.events.add('ui:updateData', ${JSON.stringify(data)});
        }
      `);
    }
  }

  /**
   * Handle CEF events
   */
  public handleCEFEvent(eventName: string, data: any): void {
    console.log(`🌐 CEF Event: ${eventName}`, data);
    
    // Route CEF events to appropriate handlers
    switch (eventName) {
      case 'ui:loginSubmit':
        mp.events.callRemote('auth:login', data);
        break;
      
      case 'ui:registerSubmit':
        mp.events.callRemote('auth:register', data);
        break;
      
      case 'ui:characterCreate':
        mp.events.callRemote('character:create', data);
        break;
      
      case 'ui:characterSelect':
        mp.events.callRemote('character:select', data);
        break;
      
      case 'ui:characterDelete':
        mp.events.callRemote('character:delete', data);
        break;
      
      case 'ui:close':
        this.hideUI();
        break;
      
      default:
        console.warn(`Unknown CEF event: ${eventName}`);
        break;
    }
  }

  /**
   * Close all browsers
   */
  public closeAll(): void {
    console.log('🖥️ Closing all UI');
    this.hideUI();
  }

  /**
   * Shutdown UI manager
   */
  public async shutdown(): Promise<void> {
    console.log('🛑 Shutting down UI Manager...');
    
    // Destroy all browsers
    for (const [name, browser] of this.browsers) {
      if (browser && browser.destroy) {
        browser.destroy();
      }
    }
    
    this.browsers.clear();
    this.activeBrowser = null;
    
    // Hide cursor
    mp.gui.cursor.show(false, false);
    mp.game.ui.displayRadar(true);
  }
}

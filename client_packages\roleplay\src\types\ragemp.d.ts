/**
 * RageMP Client Type Definitions
 */

declare global {
  // Vector3 interface
  interface Vector3Mp {
    x: number;
    y: number;
    z: number;
    distanceTo(position: Vector3Mp): number;
  }

  // Player interface
  interface PlayerMp {
    id: number;
    name: string;
    position: Vector3Mp;
    heading: number;
    health: number;
    armour: number;
    weapon: number;
    vehicle: VehicleMp | null;
    getHealth(): number;
    getArmour(): number;
    getHeading(): number;
  }

  // Vehicle interface
  interface VehicleMp {
    id: number;
    position: Vector3Mp;
    getSpeed(): number;
    getCurrentRpm(): number;
    getCurrentGear(): number;
    getIsEngineRunning(): boolean;
  }

  // Browser interface
  interface BrowserMp {
    execute(code: string): void;
    destroy(): void;
  }

  // Browsers pool interface
  interface BrowsersPool {
    new(url: string): BrowserMp;
  }

  // Events interface
  interface EventsMp {
    add(eventName: string, handler: Function): void;
    remove(eventName: string, handler?: Function): void;
    call(eventName: string, ...args: any[]): void;
    callRemote(eventName: string, ...args: any[]): void;
  }

  // Players pool interface
  interface PlayersPool {
    local: PlayerMp;
    length: number;
    toArray(): PlayerMp[];
  }

  // GUI interface
  interface GuiMp {
    cursor: {
      show(state: boolean, toggle?: boolean): void;
    };
  }

  // Game interface
  interface GameMp {
    ui: {
      displayHud(state: boolean): void;
      displayRadar(state: boolean): void;
    };
  }

  // Main MP interface
  interface Mp {
    events: EventsMp;
    players: PlayersPool;
    browsers: BrowsersPool;
    gui: GuiMp;
    game: GameMp;
    Vector3: new (x: number, y: number, z: number) => Vector3Mp;
  }

  // Global mp object
  var mp: Mp;
}

export {};

{"mappings": ";;AAAA;;;;;;;;;;CAUC;AAmBM,SAAS,0CAA4B,KAAuC;IACjF,IAAI,OAAO,aAAa,aAAa;QACnC,QAAQ,GAAG,CAAC;QACZ,OAAO;IACT;IAEA,IAAI,SAAC,KAAK,UAAE,MAAM,WAAE,OAAO,EAAC,GAAG;IAC/B,4DAA4D;IAC5D,2DAA2D;IAC3D,qBAAO,gCAAC;QAAO,OAAO,OAAO,WAAW,cAAc,QAAQ;QAAI,0BAAA;QAAyB,yBAAyB;YAAC,QAAQ,0CAA6B,QAAQ;QAAQ;;AAC5K;AAKO,SAAS,0CAA6B,MAAc,EAAE,OAAgC;IAC3F,OAAO,CAAC,6CAA6C,EAAE,KAAK,SAAS,CAAC,QAAQ,EAAE,EAAE,gCAAU,SAAS,CAAC,CAAC;AACzG;AAEA,MAAM,8BAAQ,IAAI;AAElB,SAAS,gCAAU,OAAgC;IACjD,IAAI,SAAS,4BAAM,GAAG,CAAC;IACvB,IAAI,QACF,OAAO;IAGT,sEAAsE;IACtE,IAAI,OAAO,IAAI;IACf,IAAI,SAAS,IAAI;IACjB,IAAK,IAAI,OAAO,QACd,IAAK,IAAI,OAAO,OAAO,CAAC,IAAI,CAAE;QAC5B,IAAI,IAAI,OAAO,CAAC,IAAI,CAAC,IAAI;QACzB,IAAI,IAAI,OAAO,MAAM,WAAW,KAAK,SAAS,CAAC,KAAK,EAAE,QAAQ;QAC9D,IAAI,KAAK,GAAG,CAAC,MAAM,CAAC,OAAO,GAAG,CAAC,IAAI;YACjC,IAAI,OAAO,OAAO,YAAY,CAAC,OAAO,IAAI,GAAG,KAAK,OAAO,IAAI,GAAG,KAAK,OAAO,IAAI,GAAG;YACnF,OAAO,GAAG,CAAC,GAAG;QAChB;QACA,KAAK,GAAG,CAAC;IACX;IAGF,IAAI,MAAM;IACV,IAAI,OAAO,IAAI,GAAG,GAChB,OAAO;IAET,KAAK,IAAI,CAAC,QAAQ,KAAK,IAAI,OACzB,OAAO,GAAG,KAAK,CAAC,EAAE,OAAO,CAAC,CAAC;IAE7B,IAAI,OAAO,IAAI,GAAG,GAChB,MAAM,IAAI,KAAK,CAAC,GAAG,MAAM;IAG3B,OAAO;IACP,IAAK,IAAI,OAAO,QAAS;QACvB,OAAO,CAAC,CAAC,EAAE,IAAI,GAAG,CAAC;QACnB,IAAK,IAAI,OAAO,OAAO,CAAC,IAAI,CAAE;YAC5B,IAAI,IAAI,OAAO,CAAC,IAAI,CAAC,IAAI;YACzB,IAAI,IAAI,OAAO,MAAM,WAAW,KAAK,SAAS,CAAC,KAAK,EAAE,QAAQ;YAC9D,IAAI,OAAO,GAAG,CAAC,IACb,IAAI,OAAO,GAAG,CAAC;YAEjB,OAAO,GAAG,QAAQ,IAAI,CAAC,OAAO,KAAK,SAAS,CAAC,OAAO,IAAI,CAAC,EAAE,EAAE,CAAC,CAAC;QACjE;QACA,MAAM,IAAI,KAAK,CAAC,GAAG,MAAM;IAC3B;IACA,MAAM,IAAI,KAAK,CAAC,GAAG,MAAM;IACzB,4BAAM,GAAG,CAAC,SAAS;IACnB,OAAO;AACT", "sources": ["packages/@react-aria/i18n/src/server.tsx"], "sourcesContent": ["/*\n * Copyright 2023 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport type {LocalizedString} from '@internationalized/string';\nimport React, {JSX} from 'react';\n\ntype PackageLocalizedStrings = {\n  [packageName: string]: Record<string, LocalizedString>\n};\n\ninterface PackageLocalizationProviderProps {\n  locale: string,\n  strings: PackageLocalizedStrings,\n  nonce?: string\n}\n\n/**\n * A PackageLocalizationProvider can be rendered on the server to inject the localized strings\n * needed by the client into the initial HTML.\n */\nexport function PackageLocalizationProvider(props: PackageLocalizationProviderProps): JSX.Element | null {\n  if (typeof document !== 'undefined') {\n    console.log('PackageLocalizationProvider should only be rendered on the server.');\n    return null;\n  }\n\n  let {nonce, locale, strings} = props;\n  // suppressHydrationWarning is necessary because the browser\n  // remove the nonce parameter from the DOM before hydration\n  return <script nonce={typeof window === 'undefined' ? nonce : ''} suppressHydrationWarning dangerouslySetInnerHTML={{__html: getPackageLocalizationScript(locale, strings)}} />;\n}\n\n/**\n * Returns the content for an inline `<script>` tag to inject localized strings into initial HTML.\n */\nexport function getPackageLocalizationScript(locale: string, strings: PackageLocalizedStrings): string {\n  return `window[Symbol.for('react-aria.i18n.locale')]=${JSON.stringify(locale)};{${serialize(strings)}}`;\n}\n\nconst cache = new WeakMap<PackageLocalizedStrings, string>();\n\nfunction serialize(strings: PackageLocalizedStrings): string {\n  let cached = cache.get(strings);\n  if (cached) {\n    return cached;\n  }\n\n  // Find common strings between packages and hoist them into variables.\n  let seen = new Set();\n  let common = new Map();\n  for (let pkg in strings) {\n    for (let key in strings[pkg]) {\n      let v = strings[pkg][key];\n      let s = typeof v === 'string' ? JSON.stringify(v) : v.toString();\n      if (seen.has(s) && !common.has(s)) {\n        let name = String.fromCharCode(common.size > 25 ? common.size + 97 : common.size + 65);\n        common.set(s, name);\n      }\n      seen.add(s);\n    }\n  }\n\n  let res = '';\n  if (common.size > 0) {\n    res += 'let ';\n  }\n  for (let [string, name] of common) {\n    res += `${name}=${string},`;\n  }\n  if (common.size > 0) {\n    res = res.slice(0, -1) + ';';\n  }\n\n  res += \"window[Symbol.for('react-aria.i18n.strings')]={\";\n  for (let pkg in strings) {\n    res += `'${pkg}':{`;\n    for (let key in strings[pkg]) {\n      let v = strings[pkg][key];\n      let s = typeof v === 'string' ? JSON.stringify(v) : v.toString();\n      if (common.has(s)) {\n        s = common.get(s);\n      }\n      res += `${/[ ()]/.test(key) ? JSON.stringify(key) : key}:${s},`;\n    }\n    res = res.slice(0, -1) + '},';\n  }\n  res = res.slice(0, -1) + '};';\n  cache.set(strings, res);\n  return res;\n}\n"], "names": [], "version": 3, "file": "index.mjs.map"}
{"version": 3, "file": "index.js", "mappings": "6GAIA,MAAaA,EACHC,gBACAC,cAAyC,IAAIC,IAErD,cAAuB,CAKhB,kBAAOC,GAIZ,OAHKJ,EAAaK,WAChBL,EAAaK,SAAW,IAAIL,GAEvBA,EAAaK,QACtB,CAKO,gBAAMC,GACXC,QAAQC,IAAI,2CACZC,KAAKC,oBACP,CAKQ,kBAAAA,GAENC,GAAGC,OAAOC,IAAI,iBAAmBC,IAC/BP,QAAQC,IAAI,kBAAmBM,KAIjCH,GAAGC,OAAOC,IAAI,YAAa,KACzBN,QAAQC,IAAI,kBAGdD,QAAQC,IAAI,kCACd,CAKO,EAAAO,CAAGC,EAAmBC,GACtBR,KAAKP,cAAcgB,IAAIF,IAC1BP,KAAKP,cAAciB,IAAIH,EAAW,IAGpCP,KAAKP,cAAckB,IAAIJ,GAAYK,KAAKJ,GACxCN,GAAGC,OAAOC,IAAIG,EAAWC,GAEzBV,QAAQC,IAAI,oCAAoCQ,IAClD,CAKO,GAAAM,CAAIN,EAAmBC,GAC5B,GAAKR,KAAKP,cAAcgB,IAAIF,GAI5B,GAAIC,EAAS,CACX,MAAMM,EAAWd,KAAKP,cAAckB,IAAIJ,GAClCQ,EAAQD,EAASE,QAAQR,GAC3BO,GAAS,IACXD,EAASG,OAAOF,EAAO,GACvBb,GAAGC,OAAOe,OAAOX,EAAWC,GAEhC,MAEmBR,KAAKP,cAAckB,IAAIJ,GAC/BY,QAAQC,GAAKlB,GAAGC,OAAOe,OAAOX,EAAWa,IAClDpB,KAAKP,cAAc4B,OAAOd,EAE9B,CAKO,UAAAe,CAAWf,KAAsBgB,GACtCrB,GAAGC,OAAOqB,WAAWjB,KAAcgB,EACrC,CAKO,WAAAE,CAAYC,EAAoBnB,KAAsBgB,GACvDG,GAAWA,EAAQC,SACrBD,EAAQC,QAAQ,iFAESpB,OAAeqB,KAAKC,UAAUN,0BAI3D,CAKO,cAAMO,GACXhC,QAAQC,IAAI,4CAGZ,IAAK,MAAOQ,EAAWO,KAAad,KAAKP,cACvCqB,EAASK,QAAQX,GAAWN,GAAGC,OAAOe,OAAOX,EAAWC,IAG1DR,KAAKP,cAAcsC,OACrB,EA9GF,gB,mFCAA,mBACUC,WAAY,EACZC,YAAoC,IAAIvC,IAKzC,gBAAMG,GACXC,QAAQC,IAAI,kCACZC,KAAKkC,mBACLlC,KAAKmC,gBACP,CAKQ,gBAAAD,GAENlC,KAAKiC,YAAYvB,IAAI,UAAU,GAC/BV,KAAKiC,YAAYvB,IAAI,SAAS,GAC9BV,KAAKiC,YAAYvB,IAAI,WAAW,GAChCV,KAAKiC,YAAYvB,IAAI,UAAU,GAC/BV,KAAKiC,YAAYvB,IAAI,UAAU,GAC/BV,KAAKiC,YAAYvB,IAAI,SAAS,GAC9BV,KAAKiC,YAAYvB,IAAI,QAAQ,GAC7BV,KAAKiC,YAAYvB,IAAI,YAAY,GACjCV,KAAKiC,YAAYvB,IAAI,eAAe,GACpCV,KAAKiC,YAAYvB,IAAI,WAAW,GAGhCR,GAAGkC,KAAKC,GAAGC,YAAW,GACtBpC,GAAGkC,KAAKC,GAAGE,cAAa,GAExBzC,QAAQC,IAAI,gCACd,CAKQ,cAAAoC,GAENjC,GAAGC,OAAOC,IAAI,aAAc,KAC1BJ,KAAKwC,WAIPtC,GAAGC,OAAOC,IAAI,oBAAsBqC,IAClCzC,KAAK0C,cAAcD,KAIrBvC,GAAGC,OAAOC,IAAI,iBAAmBuC,IAC/B3C,KAAK4C,WAAWD,KAGlB7C,QAAQC,IAAI,8BACd,CAKO,IAAA8C,GACA7C,KAAKgC,YACRhC,KAAKgC,WAAY,EACjBlC,QAAQC,IAAI,gBAGZG,GAAGC,OAAO2C,KAAK,cAEnB,CAKO,IAAAC,GACD/C,KAAKgC,YACPhC,KAAKgC,WAAY,EACjBlC,QAAQC,IAAI,iBAGZG,GAAGC,OAAO2C,KAAK,cAEnB,CAKO,MAAAN,GACLxC,KAAKgC,WAAahC,KAAKgC,UACvBlC,QAAQC,IAAI,qBAAsBC,KAAKgC,WAGvC9B,GAAGC,OAAO2C,KAAK,eAAgB9C,KAAKgC,UACtC,CAKO,aAAAU,CAAcD,GACnB,GAAIzC,KAAKiC,YAAYxB,IAAIgC,GAAU,CACjC,MAAMO,EAAehD,KAAKiC,YAAYtB,IAAI8B,GAC1CzC,KAAKiC,YAAYvB,IAAI+B,GAAUO,GAE/BlD,QAAQC,IAAI,kBAAkB0C,MAAaO,GAG3C9C,GAAGC,OAAO2C,KAAK,sBAAuB,CACpCL,QAASA,EACTQ,SAAUD,GAEd,CACF,CAKO,UAAAJ,CAAWD,GAEhBzC,GAAGC,OAAO2C,KAAK,mBAAoBH,EACrC,CAKO,MAAAO,GACL,IAAKlD,KAAKgC,UAAW,OAGrB,MAAMmB,EAASjD,GAAGkD,QAAQC,MAC1B,IAAKF,EAAQ,OAGb,MAAMG,EAAU,CACdC,OAAQJ,EAAOK,YACfC,MAAON,EAAOO,YACdC,SAAUR,EAAOQ,SACjBC,QAAST,EAAOU,aAChBC,UAA8B,OAAnBX,EAAOY,QAClBA,QAASZ,EAAOY,QAAU,CACxBC,MAAmC,IAA5Bb,EAAOY,QAAQE,WACtBC,IAAsC,IAAjCf,EAAOY,QAAQI,gBACpBC,KAAMjB,EAAOY,QAAQM,iBACrBC,KAAM,IACNC,SAAUpB,EAAOY,QAAQS,sBACvB,MAINxE,KAAK4C,WAAWU,EAClB,CAKA,WAAWL,GACT,OAAOjD,KAAKgC,SACd,CAKO,gBAAAyC,CAAiBhC,GACtB,OAAOzC,KAAKiC,YAAYtB,IAAI8B,KAAY,CAC1C,CAKO,cAAMX,GACXhC,QAAQC,IAAI,mCACZC,KAAK+C,OACL/C,KAAKiC,YAAYF,OACnB,E,oFC5KF,oBACU2C,iBAAkB,EAClBC,WAAkB,KAKnB,gBAAM9E,GACXC,QAAQC,IAAI,mCACZC,KAAK4E,iBACP,CAKQ,eAAAA,GAEN1E,GAAGC,OAAOC,IAAI,gBAAkByE,IAC9B7E,KAAK8E,mBAAmBD,KAI1B3E,GAAGC,OAAOC,IAAI,cAAe,KAC3BJ,KAAK+E,iBAGPjF,QAAQC,IAAI,+BACd,CAKQ,kBAAA+E,CAAmBD,GACrBA,EAASG,SACXhF,KAAK0E,iBAAkB,EACvB1E,KAAK2E,WAAaE,EAAS1B,OAC3BrD,QAAQC,IAAI,+BAGZG,GAAGC,OAAO2C,KAAK,WACf5C,GAAGC,OAAO2C,KAAK,UAAW,CAAEmC,KAAM,wBAElCjF,KAAK0E,iBAAkB,EACvB1E,KAAK2E,WAAa,KAClB7E,QAAQC,IAAI,2BAA4B8E,EAASxE,SAErD,CAKQ,YAAA0E,GACN/E,KAAK0E,iBAAkB,EACvB1E,KAAK2E,WAAa,KAClB7E,QAAQC,IAAI,iBAGZG,GAAGC,OAAO2C,KAAK,UAAW,CAAEmC,KAAM,SACpC,CAKA,iBAAWC,GACT,OAAOlF,KAAK0E,eACd,CAKA,UAAWvB,GACT,OAAOnD,KAAK2E,UACd,CAKO,cAAM7C,GACXhC,QAAQC,IAAI,oCACZC,KAAK0E,iBAAkB,EACvB1E,KAAK2E,WAAa,IACpB,E,oFCjFF,oBACU3C,WAAY,EACZmD,aAAc,EAKf,gBAAMtF,GACXC,QAAQC,IAAI,mCACZC,KAAKoF,iBACP,CAKQ,eAAAA,GAENlF,GAAGC,OAAOC,IAAI,eAAiBC,IAC7BL,KAAKqF,eAAehF,KAItBH,GAAGkC,KAAKC,GAAGC,YAAW,GACtBpC,GAAGkC,KAAKC,GAAGE,cAAa,GAExBzC,QAAQC,IAAI,+BACd,CAKQ,cAAAsF,CAAehF,GACrBP,QAAQC,IAAI,OAAOM,EAAQ4E,SAAS5E,EAAQiF,WAAWjF,EAAQA,WAG/DH,GAAGC,OAAO2C,KAAK,iBAAkBzC,EACnC,CAKO,MAAAmC,GACLxC,KAAKgC,WAAahC,KAAKgC,UACvBlC,QAAQC,IAAI,sBAAuBC,KAAKgC,WAGxC9B,GAAGC,OAAO2C,KAAK,oBAAqB9C,KAAKgC,UAC3C,CAKO,SAAAuD,GACAvF,KAAKmF,cACRnF,KAAKmF,aAAc,EACnBrF,QAAQC,IAAI,wBAGZG,GAAGsF,IAAIC,OAAO5C,MAAK,GAAM,GAGzB3C,GAAGC,OAAO2C,KAAK,oBAEnB,CAKO,UAAA4C,GACD1F,KAAKmF,cACPnF,KAAKmF,aAAc,EACnBrF,QAAQC,IAAI,wBAGZG,GAAGsF,IAAIC,OAAO5C,MAAK,GAAO,GAG1B3C,GAAGC,OAAO2C,KAAK,qBAEnB,CAKO,WAAA6C,CAAYV,EAAc5E,GAC3BA,EAAQuF,OAAOC,OAAS,GAC1B3F,GAAGC,OAAOqB,WAAW,mBAAoB,CACvCyD,KAAMA,EACN5E,QAASA,EAAQuF,SAIrB5F,KAAK0F,YACP,CAKA,WAAWzC,GACT,OAAOjD,KAAKgC,SACd,CAKA,aAAW8D,GACT,OAAO9F,KAAKmF,WACd,CAKO,cAAMrD,GACXhC,QAAQC,IAAI,oCACZC,KAAK0F,YACP,E,yFCnHF,yBACUK,gBAAuB,KACvBC,WAAoB,GAKrB,gBAAMnG,GACXC,QAAQC,IAAI,wCACZC,KAAKiG,sBACP,CAKQ,oBAAAA,GAEN/F,GAAGC,OAAOC,IAAI,iBAAmB8F,IAC/BlG,KAAKmG,mBAAmBD,KAI1BhG,GAAGC,OAAOC,IAAI,iBAAmB4F,IAC/BhG,KAAKoG,cAAcJ,KAIrB9F,GAAGC,OAAOC,IAAI,mBAAqBiG,IACjCrG,KAAKsG,gBAAgBD,KAGvBvG,QAAQC,IAAI,oCACd,CAKQ,kBAAAoG,CAAmBD,GACzBlG,KAAK+F,gBAAkBG,EACvBpG,QAAQC,IAAI,2BAA4BmG,EAAUK,UAAWL,EAAUM,UAGvEtG,GAAGC,OAAO2C,KAAK,UACjB,CAKQ,aAAAsD,CAAcJ,GACpBhG,KAAKgG,WAAaA,EAClBlG,QAAQC,IAAI,wBAAyBiG,EAAWH,OAClD,CAKQ,eAAAS,CAAgBD,GAClBrG,KAAK+F,kBACPU,OAAOC,OAAO1G,KAAK+F,gBAAiBM,GACpCvG,QAAQC,IAAI,wBAEhB,CAKO,aAAA4G,CAAcC,EAAgBC,GACnC/G,QAAQC,IAAI,0BAA2B6G,EAIzC,CAKO,YAAAE,CAAa3D,EAAkB4D,GAEpC7G,GAAGC,OAAOqB,WAAW,mBAAoB,CACvCyD,KAAM,QACN5E,QAAS0G,GAEb,CAKA,aAAWb,GACT,OAAOlG,KAAK+F,eACd,CAKA,kBAAWiB,GACT,OAAOhH,KAAKgG,UACd,CAKO,cAAMlE,GACXhC,QAAQC,IAAI,yCACZC,KAAK+F,gBAAkB,KACvB/F,KAAKgG,WAAa,EACpB,E,kFCzGF,MAAaiB,EACHzH,gBACA0H,SAAmC,IAAIxH,IACvCyH,cAAkC,KAE1C,cAAuB,CAKhB,kBAAOxH,GAIZ,OAHKsH,EAAUrH,WACbqH,EAAUrH,SAAW,IAAIqH,GAEpBA,EAAUrH,QACnB,CAKO,gBAAMC,GACXC,QAAQC,IAAI,kCACZC,KAAKoH,eACP,CAKQ,aAAAA,GAENlH,GAAGC,OAAOC,IAAI,UAAYuC,IACxB3C,KAAKqH,OAAO1E,EAAKsC,KAAMtC,EAAKA,QAI9BzC,GAAGC,OAAOC,IAAI,UAAW,KACvBJ,KAAKsH,WAIPpH,GAAGC,OAAOC,IAAI,gBAAkBuC,IAC9B3C,KAAKuH,aAAa5E,KAGpB7C,QAAQC,IAAI,6BACd,CAKQ,UAAAyH,CAAWC,GACjB,IAAKzH,KAAKkH,SAASzG,IAAIgH,GAAO,CAC5B,MAAM/F,EAAWxB,GAAGgH,SAAiBQ,IAAI,gCACzC1H,KAAKkH,SAASxG,IAAI+G,EAAM/F,GAGxBA,EAAQC,QAAQ,6QAQlB,CAEA,OAAO3B,KAAKkH,SAASvG,IAAI8G,EAC3B,CAKO,MAAAJ,CAAOpC,EAActC,GAC1B7C,QAAQC,IAAI,mBAAmBkF,KAE/B,MAAMvD,EAAU1B,KAAKwH,WAAW,QAChCxH,KAAKmH,cAAgBzF,EAGrBxB,GAAGsF,IAAIC,OAAO5C,MAAK,GAAM,GACzB3C,GAAGkC,KAAKC,GAAGE,cAAa,GAGxBb,EAAQC,QAAQ,yFAEsBC,KAAKC,UAAU,CAAEoD,OAAMtC,4BAG/D,CAKO,MAAA2E,GACLxH,QAAQC,IAAI,iBAERC,KAAKmH,gBAEPjH,GAAGsF,IAAIC,OAAO5C,MAAK,GAAO,GAC1B3C,GAAGkC,KAAKC,GAAGE,cAAa,GAGxBvC,KAAKmH,cAAcxF,QAAQ,qHAM3B3B,KAAKmH,cAAgB,KAEzB,CAKO,YAAAI,CAAa5E,GACd3C,KAAKmH,eACPnH,KAAKmH,cAAcxF,QAAQ,mGAEiBC,KAAKC,UAAUc,0BAI/D,CAKO,cAAAgF,CAAepH,EAAmBoC,GAIvC,OAHA7C,QAAQC,IAAI,iBAAiBQ,IAAaoC,GAGlCpC,GACN,IAAK,iBACHL,GAAGC,OAAOqB,WAAW,aAAcmB,GACnC,MAEF,IAAK,oBACHzC,GAAGC,OAAOqB,WAAW,gBAAiBmB,GACtC,MAEF,IAAK,qBACHzC,GAAGC,OAAOqB,WAAW,mBAAoBmB,GACzC,MAEF,IAAK,qBACHzC,GAAGC,OAAOqB,WAAW,mBAAoBmB,GACzC,MAEF,IAAK,qBACHzC,GAAGC,OAAOqB,WAAW,mBAAoBmB,GACzC,MAEF,IAAK,WACH3C,KAAKsH,SACL,MAEF,QACExH,QAAQ8H,KAAK,sBAAsBrH,KAGzC,CAKO,QAAAsH,GACL/H,QAAQC,IAAI,sBACZC,KAAKsH,QACP,CAKO,cAAMxF,GACXhC,QAAQC,IAAI,kCAGZ,IAAK,MAAO0H,EAAM/F,KAAY1B,KAAKkH,SAC7BxF,GAAWA,EAAQoG,SACrBpG,EAAQoG,UAIZ9H,KAAKkH,SAASnF,QACd/B,KAAKmH,cAAgB,KAGrBjH,GAAGsF,IAAIC,OAAO5C,MAAK,GAAO,GAC1B3C,GAAGkC,KAAKC,GAAGE,cAAa,EAC1B,EA9LF,a,cCAA,IAAYwF,E,4EAAZ,SAAYA,GACV,qBACA,mBACA,mBACA,qBACA,wBACD,CAND,CAAYA,IAAQ,WAARA,EAAQ,KAQpB,MAAaC,EACHxI,gBAA4BuI,EAASE,KAKtC,kBAAOC,CAAYC,GACxBnI,KAAKoI,SAAWD,CAClB,CAKQ,mBAAOE,GAEb,OADY,IAAIC,MACLC,cAAcC,QAAQ,IAAK,KAAKC,UAAU,EAAG,GAC1D,CAKQ,oBAAOC,CAAcP,EAAe9H,KAAoBkB,GAM9D,MAAO,IALWvB,KAAKqI,6BAKYF,MAAU9H,IAJvBkB,EAAKsE,OAAS,EAAI,IAAMtE,EAAKoH,IAAIC,GACtC,iBAARA,EAAmBhH,KAAKC,UAAU+G,EAAK,KAAM,GAAKC,OAAOD,IAChEE,KAAK,KAAO,IAGhB,CAKO,YAAOC,CAAM1I,KAAoBkB,GACtC,GAAIvB,KAAKoI,UAAYL,EAASiB,MAAO,CACnC,MAAMC,EAAYjJ,KAAK0I,cAAc,QAASrI,KAAYkB,GAC1DzB,QAAQC,IAAIkJ,EACd,CACF,CAKO,WAAOC,CAAK7I,KAAoBkB,GACrC,GAAIvB,KAAKoI,UAAYL,EAASE,KAAM,CAClC,MAAMgB,EAAYjJ,KAAK0I,cAAc,OAAQrI,KAAYkB,GACzDzB,QAAQC,IAAIkJ,EACd,CACF,CAKO,WAAOrB,CAAKvH,KAAoBkB,GACrC,GAAIvB,KAAKoI,UAAYL,EAASoB,KAAM,CAClC,MAAMF,EAAYjJ,KAAK0I,cAAc,OAAQrI,KAAYkB,GACzDzB,QAAQ8H,KAAKqB,EACf,CACF,CAKO,YAAOG,CAAM/I,KAAoBkB,GACtC,GAAIvB,KAAKoI,UAAYL,EAASsB,MAAO,CACnC,MAAMJ,EAAYjJ,KAAK0I,cAAc,QAASrI,KAAYkB,GAC1DzB,QAAQsJ,MAAMH,EAChB,CACF,CAKO,cAAOjE,CAAQ3E,KAAoBkB,GACxC,GAAIvB,KAAKoI,UAAYL,EAASuB,QAAS,CACrC,MAAML,EAAYjJ,KAAK0I,cAAc,UAAWrI,KAAYkB,GAC5DzB,QAAQC,IAAIkJ,EACd,CACF,EA9EF,U,GCXIM,EAA2B,CAAC,EAGhC,SAASC,EAAoBC,GAE5B,IAAIC,EAAeH,EAAyBE,GAC5C,QAAqBE,IAAjBD,EACH,OAAOA,EAAaE,QAGrB,IAAIC,EAASN,EAAyBE,GAAY,CAGjDG,QAAS,CAAC,GAOX,OAHAE,EAAoBL,GAAUI,EAAQA,EAAOD,QAASJ,GAG/CK,EAAOD,OACf,C,MCjBA,eACA,SACA,SACA,SACA,SACA,SACA,SAEA,MAAMG,EACIvK,gBACAwK,SAA6B,IAAItK,IACjCuK,eAAgB,EAExB,WAAAC,GACE,GAAIH,EAAenK,SACjB,OAAOmK,EAAenK,SAExBmK,EAAenK,SAAWI,IAC5B,CAKO,gBAAMH,GACX,IACE,EAAAmI,OAAOkB,KAAK,+CAGNlJ,KAAKmK,qBAGXnK,KAAKoK,oBAELpK,KAAKiK,eAAgB,EACrB,EAAAjC,OAAOhD,QAAQ,iDAEjB,CAAE,MAAOoE,GAEP,MADA,EAAApB,OAAOoB,MAAM,4BAA6BA,GACpCA,CACR,CACF,CAKQ,wBAAMe,GACZ,EAAAnC,OAAOkB,KAAK,+BAEZ,MAAMmB,EAAiB,CACrB,CAAE5C,KAAM,QAAS7H,SAAU,EAAAL,aAAaI,eACxC,CAAE8H,KAAM,KAAM7H,SAAU,EAAAqH,UAAUtH,eAClC,CAAE8H,KAAM,OAAQ7H,SAAU,IAAI,EAAA0K,aAC9B,CAAE7C,KAAM,YAAa7H,SAAU,IAAI,EAAA2K,kBACnC,CAAE9C,KAAM,OAAQ7H,SAAU,IAAI,EAAA4K,aAC9B,CAAE/C,KAAM,MAAO7H,SAAU,IAAI,EAAA6K,aAG/B,IAAK,MAAM,KAAEhD,EAAI,SAAE7H,KAAcyK,EAC/B,IACE,EAAArC,OAAOkB,KAAK,gBAAgBzB,sBACtB7H,EAASC,aACfG,KAAKgK,SAAStJ,IAAI+G,EAAM7H,GACxB,EAAAoI,OAAOhD,QAAQ,OAAOyC,mBACxB,CAAE,MAAO2B,GAEP,MADA,EAAApB,OAAOoB,MAAM,sBAAsB3B,aAAiB2B,GAC9CA,CACR,CAGF,EAAApB,OAAOhD,QAAQ,0CACjB,CAKQ,iBAAAoF,GACN,EAAApC,OAAOkB,KAAK,0CAGZhJ,GAAGC,OAAOC,IAAI,cAAe,KAC3B,EAAA4H,OAAOkB,KAAK,kBAGZ,MAAMwB,EAAa1K,KAAKgK,SAASrJ,IAAI,OACjC+J,GACFA,EAAW7H,SAKf3C,GAAGC,OAAOC,IAAI,cAAe,CAAC+C,EAAkByD,EAAgBC,KAC9D,EAAAmB,OAAOkB,KAAK,wBAAwBtC,MAGpC,MAAM+D,EAAmB3K,KAAKgK,SAASrJ,IAAI,aACvCgK,GACFA,EAAiBhE,cAAcC,EAAQC,KAK3C3G,GAAGC,OAAOC,IAAI,SAAU,KAEtB,MAAMsK,EAAa1K,KAAKgK,SAASrJ,IAAI,OACjC+J,GACFA,EAAWxH,WAKfhD,GAAGC,OAAOC,IAAI,UAAYwK,IACxB5K,KAAK6K,cAAcD,KAGrB1K,GAAGC,OAAOC,IAAI,QAAUwK,IACtB5K,KAAK8K,YAAYF,KAInB1K,GAAGC,OAAOC,IAAI,WAAY,CAACG,EAAmBoC,KAC5C,MAAMoI,EAAY/K,KAAKgK,SAASrJ,IAAI,MAChCoK,GACFA,EAAUpD,eAAepH,EAAWoC,KAKxCzC,GAAGC,OAAOC,IAAI,WAAY,CAACG,EAAmByK,KAAmBzJ,KAC/DvB,KAAKiL,cAAc1K,EAAWyK,KAAWzJ,KAG3C,EAAAyG,OAAOhD,QAAQ,yCACjB,CAKQ,aAAA6F,CAAcD,GAEpB,GAAgB,MAAZA,EAAiB,CACnB,MAAMM,EAAclL,KAAKgK,SAASrJ,IAAI,QAClCuK,GACFA,EAAY1I,QAEhB,CAGA,GAAgB,MAAZoI,EAAiB,CACnB,MAAMF,EAAa1K,KAAKgK,SAASrJ,IAAI,OACjC+J,GACFA,EAAWlI,QAEf,CAGA,GAAgB,KAAZoI,EAAgB,CAClB,MAAMM,EAAclL,KAAKgK,SAASrJ,IAAI,QAClCuK,GACFA,EAAY3F,WAEhB,CAGA,GAAgB,KAAZqF,EAAgB,CAClB,MAAMG,EAAY/K,KAAKgK,SAASrJ,IAAI,MAChCoK,GACFA,EAAUlD,UAEd,CACF,CAKQ,WAAAiD,CAAYF,GAEpB,CAKQ,mBAAMK,CAAc1K,EAAmByK,KAAmBzJ,GAChE,IACE,IACI6H,EADA+B,EAAc,KAIlB,OAAQ5K,GACN,IAAK,oBACH4K,EAASjL,GAAGkD,QAAQC,MAAMM,SAC1B,MAEF,IAAK,kBACHwH,EAASjL,GAAGkD,QAAQC,MAAME,OAC1B,MAEF,IAAK,oBACH4H,EAAsC,OAA7BjL,GAAGkD,QAAQC,MAAMU,QAC1B,MAEF,QACEqF,EAAQ,qBAAqB7I,IAKjCL,GAAGC,OAAOqB,WAAW,gBAAgBwJ,IAAUG,EAAQ/B,EAEzD,CAAE,MAAOgC,GACPlL,GAAGC,OAAOqB,WAAW,gBAAgBwJ,IAAU,KAAOI,EAAc/K,QACtE,CACF,CAKO,UAAAgL,CAAc5D,GACnB,OAAOzH,KAAKgK,SAASrJ,IAAI8G,EAC3B,CAKA,eAAW6D,GACT,OAAOtL,KAAKiK,aACd,CAKO,cAAMnI,GACX,EAAAkG,OAAOkB,KAAK,8BAGZ,IAAK,MAAOzB,EAAM8D,KAAYvL,KAAKgK,SACjC,IACMuB,EAAQzJ,iBACJyJ,EAAQzJ,WACd,EAAAkG,OAAOkB,KAAK,OAAOzB,+BAEvB,CAAE,MAAO2B,GACP,EAAApB,OAAOoB,MAAM,2BAA2B3B,aAAiB2B,EAC3D,CAGF,EAAApB,OAAOhD,QAAQ,6BACjB,EAIF,IAAIwG,EAGJ,WACE,IACEA,EAAiB,IAAIzB,QACfyB,EAAe3L,YACvB,CAAE,MAAOuJ,GACP,EAAApB,OAAOoB,MAAM,+BAAgCA,EAC/C,CACD,EAPD,E", "sources": ["webpack://@ragemp-rp/client/./src/events/EventManager.ts", "webpack://@ragemp-rp/client/./src/modules/HUDManager.ts", "webpack://@ragemp-rp/client/./src/modules/AuthManager.ts", "webpack://@ragemp-rp/client/./src/modules/ChatManager.ts", "webpack://@ragemp-rp/client/./src/modules/CharacterManager.ts", "webpack://@ragemp-rp/client/./src/ui/UIManager.ts", "webpack://@ragemp-rp/client/./src/utils/Logger.ts", "webpack://@ragemp-rp/client/webpack/bootstrap", "webpack://@ragemp-rp/client/./src/index.ts"], "sourcesContent": ["/**\n * Client-side Event Manager for handling RageMP events\n */\n\nexport class EventManager {\n  private static instance: EventManager;\n  private eventHandlers: Map<string, Function[]> = new Map();\n\n  private constructor() {}\n\n  /**\n   * Get singleton instance\n   */\n  public static getInstance(): EventManager {\n    if (!EventManager.instance) {\n      EventManager.instance = new EventManager();\n    }\n    return EventManager.instance;\n  }\n\n  /**\n   * Initialize event manager\n   */\n  public async initialize(): Promise<void> {\n    console.log('🎯 Initializing Client Event Manager...');\n    this.setupDefaultEvents();\n  }\n\n  /**\n   * Setup default RageMP events\n   */\n  private setupDefaultEvents(): void {\n    // Server to client events\n    mp.events.add('server:message', (message: string) => {\n      console.log('Server message:', message);\n    });\n\n    // CEF events\n    mp.events.add('cef:ready', () => {\n      console.log('CEF is ready');\n    });\n\n    console.log('✅ Default events setup complete');\n  }\n\n  /**\n   * Register event handler\n   */\n  public on(eventName: string, handler: Function): void {\n    if (!this.eventHandlers.has(eventName)) {\n      this.eventHandlers.set(eventName, []);\n    }\n    \n    this.eventHandlers.get(eventName)!.push(handler);\n    mp.events.add(eventName, handler);\n    \n    console.log(`📡 Registered handler for event: ${eventName}`);\n  }\n\n  /**\n   * Unregister event handler\n   */\n  public off(eventName: string, handler?: Function): void {\n    if (!this.eventHandlers.has(eventName)) {\n      return;\n    }\n\n    if (handler) {\n      const handlers = this.eventHandlers.get(eventName)!;\n      const index = handlers.indexOf(handler);\n      if (index > -1) {\n        handlers.splice(index, 1);\n        mp.events.remove(eventName, handler);\n      }\n    } else {\n      // Remove all handlers for this event\n      const handlers = this.eventHandlers.get(eventName)!;\n      handlers.forEach(h => mp.events.remove(eventName, h));\n      this.eventHandlers.delete(eventName);\n    }\n  }\n\n  /**\n   * Call server event\n   */\n  public callServer(eventName: string, ...args: any[]): void {\n    mp.events.callRemote(eventName, ...args);\n  }\n\n  /**\n   * Call browser event\n   */\n  public callBrowser(browser: BrowserMp, eventName: string, ...args: any[]): void {\n    if (browser && browser.execute) {\n      browser.execute(`\n        if (window.mp && window.mp.trigger) {\n          window.mp.trigger('${eventName}', ${JSON.stringify(args)});\n        }\n      `);\n    }\n  }\n\n  /**\n   * Shutdown event manager\n   */\n  public async shutdown(): Promise<void> {\n    console.log('🛑 Shutting down Client Event Manager...');\n    \n    // Remove all event handlers\n    for (const [eventName, handlers] of this.eventHandlers) {\n      handlers.forEach(handler => mp.events.remove(eventName, handler));\n    }\n    \n    this.eventHandlers.clear();\n  }\n}\n", "/**\n * Client-side HUD Manager\n */\n\nexport class HUDManager {\n  private isVisible = true;\n  private hudElements: Map<string, boolean> = new Map();\n\n  /**\n   * Initialize HUD manager\n   */\n  public async initialize(): Promise<void> {\n    console.log('📊 Initializing HUD Manager...');\n    this.setupHUDElements();\n    this.setupHUDEvents();\n  }\n\n  /**\n   * Setup HUD elements\n   */\n  private setupHUDElements(): void {\n    // Initialize HUD element visibility\n    this.hudElements.set('health', true);\n    this.hudElements.set('armor', true);\n    this.hudElements.set('stamina', true);\n    this.hudElements.set('hunger', true);\n    this.hudElements.set('thirst', true);\n    this.hudElements.set('money', true);\n    this.hudElements.set('time', true);\n    this.hudElements.set('location', true);\n    this.hudElements.set('speedometer', true);\n    this.hudElements.set('minimap', true);\n\n    // Disable default GTA HUD elements\n    mp.game.ui.displayHud(false);\n    mp.game.ui.displayRadar(true);\n    \n    console.log('✅ HUD elements setup complete');\n  }\n\n  /**\n   * Setup HUD events\n   */\n  private setupHUDEvents(): void {\n    // HUD visibility toggle from server\n    mp.events.add('hud:toggle', () => {\n      this.toggle();\n    });\n\n    // HUD element toggle from server\n    mp.events.add('hud:toggleElement', (element: string) => {\n      this.toggleElement(element);\n    });\n\n    // HUD data update from server\n    mp.events.add('hud:updateData', (data: any) => {\n      this.updateData(data);\n    });\n\n    console.log('✅ HUD events setup complete');\n  }\n\n  /**\n   * Show HUD\n   */\n  public show(): void {\n    if (!this.isVisible) {\n      this.isVisible = true;\n      console.log('📊 HUD shown');\n      \n      // Send show event to UI\n      mp.events.call('ui:hudShow');\n    }\n  }\n\n  /**\n   * Hide HUD\n   */\n  public hide(): void {\n    if (this.isVisible) {\n      this.isVisible = false;\n      console.log('📊 HUD hidden');\n      \n      // Send hide event to UI\n      mp.events.call('ui:hudHide');\n    }\n  }\n\n  /**\n   * Toggle HUD visibility\n   */\n  public toggle(): void {\n    this.isVisible = !this.isVisible;\n    console.log('📊 HUD visibility:', this.isVisible);\n    \n    // Send toggle event to UI\n    mp.events.call('ui:hudToggle', this.isVisible);\n  }\n\n  /**\n   * Toggle specific HUD element\n   */\n  public toggleElement(element: string): void {\n    if (this.hudElements.has(element)) {\n      const currentState = this.hudElements.get(element)!;\n      this.hudElements.set(element, !currentState);\n      \n      console.log(`📊 HUD element ${element}:`, !currentState);\n      \n      // Send element toggle event to UI\n      mp.events.call('ui:hudToggleElement', {\n        element: element,\n        visible: !currentState\n      });\n    }\n  }\n\n  /**\n   * Update HUD data\n   */\n  public updateData(data: any): void {\n    // Send data update to UI\n    mp.events.call('ui:hudUpdateData', data);\n  }\n\n  /**\n   * Update HUD continuously\n   */\n  public update(): void {\n    if (!this.isVisible) return;\n\n    // Get player data\n    const player = mp.players.local;\n    if (!player) return;\n\n    // Prepare HUD data\n    const hudData = {\n      health: player.getHealth(),\n      armor: player.getArmour(),\n      position: player.position,\n      heading: player.getHeading(),\n      inVehicle: player.vehicle !== null,\n      vehicle: player.vehicle ? {\n        speed: player.vehicle.getSpeed() * 3.6, // Convert to km/h\n        rpm: player.vehicle.getCurrentRpm() * 8000,\n        gear: player.vehicle.getCurrentGear(),\n        fuel: 100, // This would come from vehicle data\n        engineOn: player.vehicle.getIsEngineRunning()\n      } : null\n    };\n\n    // Send update to UI\n    this.updateData(hudData);\n  }\n\n  /**\n   * Get HUD visibility\n   */\n  public get visible(): boolean {\n    return this.isVisible;\n  }\n\n  /**\n   * Get element visibility\n   */\n  public isElementVisible(element: string): boolean {\n    return this.hudElements.get(element) || false;\n  }\n\n  /**\n   * Shutdown HUD manager\n   */\n  public async shutdown(): Promise<void> {\n    console.log('🛑 Shutting down HUD Manager...');\n    this.hide();\n    this.hudElements.clear();\n  }\n}\n", "/**\n * Client-side Authentication Manager\n */\n\nexport class AuthManager {\n  private isAuthenticated = false;\n  private playerData: any = null;\n\n  /**\n   * Initialize authentication manager\n   */\n  public async initialize(): Promise<void> {\n    console.log('🔐 Initializing Auth Manager...');\n    this.setupAuthEvents();\n  }\n\n  /**\n   * Setup authentication events\n   */\n  private setupAuthEvents(): void {\n    // Authentication response from server\n    mp.events.add('auth:response', (response: any) => {\n      this.handleAuthResponse(response);\n    });\n\n    // Logout event from server\n    mp.events.add('auth:logout', () => {\n      this.handleLogout();\n    });\n\n    console.log('✅ Auth events setup complete');\n  }\n\n  /**\n   * Handle authentication response\n   */\n  private handleAuthResponse(response: any): void {\n    if (response.success) {\n      this.isAuthenticated = true;\n      this.playerData = response.player;\n      console.log('✅ Authentication successful');\n      \n      // Hide login UI and show character selection\n      mp.events.call('ui:hide');\n      mp.events.call('ui:show', { type: 'characterSelector' });\n    } else {\n      this.isAuthenticated = false;\n      this.playerData = null;\n      console.log('❌ Authentication failed:', response.message);\n    }\n  }\n\n  /**\n   * Handle logout\n   */\n  private handleLogout(): void {\n    this.isAuthenticated = false;\n    this.playerData = null;\n    console.log('🚪 Logged out');\n    \n    // Show login UI\n    mp.events.call('ui:show', { type: 'login' });\n  }\n\n  /**\n   * Check if player is authenticated\n   */\n  public get authenticated(): boolean {\n    return this.isAuthenticated;\n  }\n\n  /**\n   * Get player data\n   */\n  public get player(): any {\n    return this.playerData;\n  }\n\n  /**\n   * Shutdown authentication manager\n   */\n  public async shutdown(): Promise<void> {\n    console.log('🛑 Shutting down Auth Manager...');\n    this.isAuthenticated = false;\n    this.playerData = null;\n  }\n}\n", "/**\n * Client-side Chat Manager\n */\n\nexport class ChatManager {\n  private isVisible = true;\n  private isInputOpen = false;\n\n  /**\n   * Initialize chat manager\n   */\n  public async initialize(): Promise<void> {\n    console.log('💬 Initializing Chat Manager...');\n    this.setupChatEvents();\n  }\n\n  /**\n   * Setup chat events\n   */\n  private setupChatEvents(): void {\n    // Chat message from server\n    mp.events.add('chat:message', (message: any) => {\n      this.displayMessage(message);\n    });\n\n    // Disable default RageMP chat\n    mp.game.ui.displayHud(false);\n    mp.game.ui.displayRadar(true);\n\n    console.log('✅ Chat events setup complete');\n  }\n\n  /**\n   * Display chat message\n   */\n  private displayMessage(message: any): void {\n    console.log(`💬 [${message.type}] ${message.sender}: ${message.message}`);\n    \n    // Send message to UI\n    mp.events.call('ui:chatMessage', message);\n  }\n\n  /**\n   * Toggle chat visibility\n   */\n  public toggle(): void {\n    this.isVisible = !this.isVisible;\n    console.log('💬 Chat visibility:', this.isVisible);\n    \n    // Send visibility update to UI\n    mp.events.call('ui:chatVisibility', this.isVisible);\n  }\n\n  /**\n   * Open chat input\n   */\n  public openInput(): void {\n    if (!this.isInputOpen) {\n      this.isInputOpen = true;\n      console.log('💬 Chat input opened');\n      \n      // Show cursor for chat input\n      mp.gui.cursor.show(true, true);\n      \n      // Send input open event to UI\n      mp.events.call('ui:chatInputOpen');\n    }\n  }\n\n  /**\n   * Close chat input\n   */\n  public closeInput(): void {\n    if (this.isInputOpen) {\n      this.isInputOpen = false;\n      console.log('💬 Chat input closed');\n      \n      // Hide cursor\n      mp.gui.cursor.show(false, false);\n      \n      // Send input close event to UI\n      mp.events.call('ui:chatInputClose');\n    }\n  }\n\n  /**\n   * Send chat message\n   */\n  public sendMessage(type: string, message: string): void {\n    if (message.trim().length > 0) {\n      mp.events.callRemote('chat:sendMessage', {\n        type: type,\n        message: message.trim()\n      });\n    }\n    \n    this.closeInput();\n  }\n\n  /**\n   * Get chat visibility\n   */\n  public get visible(): boolean {\n    return this.isVisible;\n  }\n\n  /**\n   * Get input state\n   */\n  public get inputOpen(): boolean {\n    return this.isInputOpen;\n  }\n\n  /**\n   * Shutdown chat manager\n   */\n  public async shutdown(): Promise<void> {\n    console.log('🛑 Shutting down Chat Manager...');\n    this.closeInput();\n  }\n}\n", "/**\n * Client-side Character Manager\n */\n\nexport class CharacterManager {\n  private activeCharacter: any = null;\n  private characters: any[] = [];\n\n  /**\n   * Initialize character manager\n   */\n  public async initialize(): Promise<void> {\n    console.log('👤 Initializing Character Manager...');\n    this.setupCharacterEvents();\n  }\n\n  /**\n   * Setup character events\n   */\n  private setupCharacterEvents(): void {\n    // Character data from server\n    mp.events.add('character:data', (character: any) => {\n      this.setActiveCharacter(character);\n    });\n\n    // Character list from server\n    mp.events.add('character:list', (characters: any[]) => {\n      this.setCharacters(characters);\n    });\n\n    // Character update from server\n    mp.events.add('character:update', (updates: any) => {\n      this.updateCharacter(updates);\n    });\n\n    console.log('✅ Character events setup complete');\n  }\n\n  /**\n   * Set active character\n   */\n  private setActiveCharacter(character: any): void {\n    this.activeCharacter = character;\n    console.log('👤 Active character set:', character.firstName, character.lastName);\n    \n    // Hide character selection UI and show HUD\n    mp.events.call('ui:hide');\n  }\n\n  /**\n   * Set characters list\n   */\n  private setCharacters(characters: any[]): void {\n    this.characters = characters;\n    console.log('👥 Characters loaded:', characters.length);\n  }\n\n  /**\n   * Update character data\n   */\n  private updateCharacter(updates: any): void {\n    if (this.activeCharacter) {\n      Object.assign(this.activeCharacter, updates);\n      console.log('👤 Character updated');\n    }\n  }\n\n  /**\n   * Handle player death\n   */\n  public onPlayerDeath(reason: number, killer: PlayerMp): void {\n    console.log('💀 Player died, reason:', reason);\n    \n    // Handle death logic here\n    // Show death screen, respawn options, etc.\n  }\n\n  /**\n   * Handle player chat\n   */\n  public onPlayerChat(player: PlayerMp, text: string): void {\n    // Handle chat through server\n    mp.events.callRemote('chat:sendMessage', {\n      type: 'local',\n      message: text\n    });\n  }\n\n  /**\n   * Get active character\n   */\n  public get character(): any {\n    return this.activeCharacter;\n  }\n\n  /**\n   * Get characters list\n   */\n  public get charactersList(): any[] {\n    return this.characters;\n  }\n\n  /**\n   * Shutdown character manager\n   */\n  public async shutdown(): Promise<void> {\n    console.log('🛑 Shutting down Character Manager...');\n    this.activeCharacter = null;\n    this.characters = [];\n  }\n}\n", "/**\n * UI Manager for handling CEF browsers and UI interactions\n */\n\nexport class UIManager {\n  private static instance: UIManager;\n  private browsers: Map<string, BrowserMp> = new Map();\n  private activeBrowser: BrowserMp | null = null;\n\n  private constructor() {}\n\n  /**\n   * Get singleton instance\n   */\n  public static getInstance(): UIManager {\n    if (!UIManager.instance) {\n      UIManager.instance = new UIManager();\n    }\n    return UIManager.instance;\n  }\n\n  /**\n   * Initialize UI manager\n   */\n  public async initialize(): Promise<void> {\n    console.log('🖥️ Initializing UI Manager...');\n    this.setupUIEvents();\n  }\n\n  /**\n   * Setup UI-related events\n   */\n  private setupUIEvents(): void {\n    // Show UI event from server\n    mp.events.add('ui:show', (data: any) => {\n      this.showUI(data.type, data.data);\n    });\n\n    // Hide UI event from server\n    mp.events.add('ui:hide', () => {\n      this.hideUI();\n    });\n\n    // Update UI data event from server\n    mp.events.add('ui:updateData', (data: any) => {\n      this.updateUIData(data);\n    });\n\n    console.log('✅ UI events setup complete');\n  }\n\n  /**\n   * Create or get browser instance\n   */\n  private getBrowser(name: string): BrowserMp {\n    if (!this.browsers.has(name)) {\n      const browser = (mp.browsers as any).new('package://ui/dist/index.html');\n      this.browsers.set(name, browser);\n      \n      // Setup browser events\n      browser.execute(`\n        window.mp = window.mp || {};\n        window.mp.trigger = function(eventName, ...args) {\n          if (window.mp.events && window.mp.events.call) {\n            window.mp.events.call('browserToClient', eventName, ...args);\n          }\n        };\n      `);\n    }\n    \n    return this.browsers.get(name)!;\n  }\n\n  /**\n   * Show UI\n   */\n  public showUI(type: string, data?: any): void {\n    console.log(`🖥️ Showing UI: ${type}`);\n    \n    const browser = this.getBrowser('main');\n    this.activeBrowser = browser;\n    \n    // Show cursor and disable game controls\n    mp.gui.cursor.show(true, true);\n    mp.game.ui.displayRadar(false);\n    \n    // Send show event to React app\n    browser.execute(`\n      if (window.mp && window.mp.events) {\n        window.mp.events.add('ui:show', ${JSON.stringify({ type, data })});\n      }\n    `);\n  }\n\n  /**\n   * Hide UI\n   */\n  public hideUI(): void {\n    console.log('🖥️ Hiding UI');\n    \n    if (this.activeBrowser) {\n      // Hide cursor and enable game controls\n      mp.gui.cursor.show(false, false);\n      mp.game.ui.displayRadar(true);\n      \n      // Send hide event to React app\n      this.activeBrowser.execute(`\n        if (window.mp && window.mp.events) {\n          window.mp.events.add('ui:hide', {});\n        }\n      `);\n      \n      this.activeBrowser = null;\n    }\n  }\n\n  /**\n   * Update UI data\n   */\n  public updateUIData(data: any): void {\n    if (this.activeBrowser) {\n      this.activeBrowser.execute(`\n        if (window.mp && window.mp.events) {\n          window.mp.events.add('ui:updateData', ${JSON.stringify(data)});\n        }\n      `);\n    }\n  }\n\n  /**\n   * Handle CEF events\n   */\n  public handleCEFEvent(eventName: string, data: any): void {\n    console.log(`🌐 CEF Event: ${eventName}`, data);\n    \n    // Route CEF events to appropriate handlers\n    switch (eventName) {\n      case 'ui:loginSubmit':\n        mp.events.callRemote('auth:login', data);\n        break;\n      \n      case 'ui:registerSubmit':\n        mp.events.callRemote('auth:register', data);\n        break;\n      \n      case 'ui:characterCreate':\n        mp.events.callRemote('character:create', data);\n        break;\n      \n      case 'ui:characterSelect':\n        mp.events.callRemote('character:select', data);\n        break;\n      \n      case 'ui:characterDelete':\n        mp.events.callRemote('character:delete', data);\n        break;\n      \n      case 'ui:close':\n        this.hideUI();\n        break;\n      \n      default:\n        console.warn(`Unknown CEF event: ${eventName}`);\n        break;\n    }\n  }\n\n  /**\n   * Close all browsers\n   */\n  public closeAll(): void {\n    console.log('🖥️ Closing all UI');\n    this.hideUI();\n  }\n\n  /**\n   * Shutdown UI manager\n   */\n  public async shutdown(): Promise<void> {\n    console.log('🛑 Shutting down UI Manager...');\n    \n    // Destroy all browsers\n    for (const [name, browser] of this.browsers) {\n      if (browser && browser.destroy) {\n        browser.destroy();\n      }\n    }\n    \n    this.browsers.clear();\n    this.activeBrowser = null;\n    \n    // Hide cursor\n    mp.gui.cursor.show(false, false);\n    mp.game.ui.displayRadar(true);\n  }\n}\n", "/**\n * Client-side Logger utility\n */\n\nexport enum LogLevel {\n  DEBUG = 0,\n  INFO = 1,\n  WARN = 2,\n  ERROR = 3,\n  SUCCESS = 4\n}\n\nexport class Logger {\n  private static logLevel: LogLevel = LogLevel.INFO;\n\n  /**\n   * Set the minimum log level\n   */\n  public static setLogLevel(level: LogLevel): void {\n    this.logLevel = level;\n  }\n\n  /**\n   * Get current timestamp\n   */\n  private static getTimestamp(): string {\n    const now = new Date();\n    return now.toISOString().replace('T', ' ').substring(0, 19);\n  }\n\n  /**\n   * Format log message\n   */\n  private static formatMessage(level: string, message: string, ...args: any[]): string {\n    const timestamp = this.getTimestamp();\n    const formattedArgs = args.length > 0 ? ' ' + args.map(arg => \n      typeof arg === 'object' ? JSON.stringify(arg, null, 2) : String(arg)\n    ).join(' ') : '';\n    \n    return `[${timestamp}] [CLIENT] [${level}] ${message}${formattedArgs}`;\n  }\n\n  /**\n   * Log debug message\n   */\n  public static debug(message: string, ...args: any[]): void {\n    if (this.logLevel <= LogLevel.DEBUG) {\n      const formatted = this.formatMessage('DEBUG', message, ...args);\n      console.log(formatted);\n    }\n  }\n\n  /**\n   * Log info message\n   */\n  public static info(message: string, ...args: any[]): void {\n    if (this.logLevel <= LogLevel.INFO) {\n      const formatted = this.formatMessage('INFO', message, ...args);\n      console.log(formatted);\n    }\n  }\n\n  /**\n   * Log warning message\n   */\n  public static warn(message: string, ...args: any[]): void {\n    if (this.logLevel <= LogLevel.WARN) {\n      const formatted = this.formatMessage('WARN', message, ...args);\n      console.warn(formatted);\n    }\n  }\n\n  /**\n   * Log error message\n   */\n  public static error(message: string, ...args: any[]): void {\n    if (this.logLevel <= LogLevel.ERROR) {\n      const formatted = this.formatMessage('ERROR', message, ...args);\n      console.error(formatted);\n    }\n  }\n\n  /**\n   * Log success message\n   */\n  public static success(message: string, ...args: any[]): void {\n    if (this.logLevel <= LogLevel.SUCCESS) {\n      const formatted = this.formatMessage('SUCCESS', message, ...args);\n      console.log(formatted);\n    }\n  }\n}\n", "// The module cache\nvar __webpack_module_cache__ = {};\n\n// The require function\nfunction __webpack_require__(moduleId) {\n\t// Check if module is in cache\n\tvar cachedModule = __webpack_module_cache__[moduleId];\n\tif (cachedModule !== undefined) {\n\t\treturn cachedModule.exports;\n\t}\n\t// Create a new module (and put it into the cache)\n\tvar module = __webpack_module_cache__[moduleId] = {\n\t\t// no module.id needed\n\t\t// no module.loaded needed\n\t\texports: {}\n\t};\n\n\t// Execute the module function\n\t__webpack_modules__[moduleId](module, module.exports, __webpack_require__);\n\n\t// Return the exports of the module\n\treturn module.exports;\n}\n\n", "/**\n * RageMP Roleplay Client\n * Main entry point for the client-side application\n */\n\nimport { EventManager } from './events/EventManager';\nimport { UIManager } from './ui/UIManager';\nimport { AuthManager } from './modules/AuthManager';\nimport { CharacterManager } from './modules/CharacterManager';\nimport { ChatManager } from './modules/ChatManager';\nimport { HUDManager } from './modules/HUDManager';\nimport { Logger } from './utils/Logger';\n\nclass RoleplayClient {\n  private static instance: RoleplayClient;\n  private managers: Map<string, any> = new Map();\n  private isInitialized = false;\n\n  constructor() {\n    if (RoleplayClient.instance) {\n      return RoleplayClient.instance;\n    }\n    RoleplayClient.instance = this;\n  }\n\n  /**\n   * Initialize the client\n   */\n  public async initialize(): Promise<void> {\n    try {\n      Logger.info('🚀 Starting RageMP Roleplay Client...');\n\n      // Initialize core managers\n      await this.initializeManagers();\n\n      // Setup global event handlers\n      this.setupGlobalEvents();\n\n      this.isInitialized = true;\n      Logger.success('✅ RageMP Roleplay Client started successfully!');\n      \n    } catch (error) {\n      Logger.error('❌ Failed to start client:', error);\n      throw error;\n    }\n  }\n\n  /**\n   * Initialize all managers\n   */\n  private async initializeManagers(): Promise<void> {\n    Logger.info('🔧 Initializing managers...');\n\n    const managerClasses = [\n      { name: 'event', instance: EventManager.getInstance() },\n      { name: 'ui', instance: UIManager.getInstance() },\n      { name: 'auth', instance: new AuthManager() },\n      { name: 'character', instance: new CharacterManager() },\n      { name: 'chat', instance: new ChatManager() },\n      { name: 'hud', instance: new HUDManager() }\n    ];\n\n    for (const { name, instance } of managerClasses) {\n      try {\n        Logger.info(`  📦 Loading ${name} manager...`);\n        await instance.initialize();\n        this.managers.set(name, instance);\n        Logger.success(`  ✅ ${name} manager loaded`);\n      } catch (error) {\n        Logger.error(`  ❌ Failed to load ${name} manager:`, error);\n        throw error;\n      }\n    }\n\n    Logger.success('✅ All managers initialized successfully');\n  }\n\n  /**\n   * Setup global event handlers\n   */\n  private setupGlobalEvents(): void {\n    Logger.info('🎯 Setting up global event handlers...');\n\n    // Player spawn event\n    mp.events.add('playerSpawn', () => {\n      Logger.info('Player spawned');\n      \n      // Initialize HUD\n      const hudManager = this.managers.get('hud') as HUDManager;\n      if (hudManager) {\n        hudManager.show();\n      }\n    });\n\n    // Player death event\n    mp.events.add('playerDeath', (player: PlayerMp, reason: number, killer: PlayerMp) => {\n      Logger.info(`Player died (reason: ${reason})`);\n      \n      // Handle death through character manager\n      const characterManager = this.managers.get('character') as CharacterManager;\n      if (characterManager) {\n        characterManager.onPlayerDeath(reason, killer);\n      }\n    });\n\n    // Render event for continuous updates\n    mp.events.add('render', () => {\n      // Update HUD\n      const hudManager = this.managers.get('hud') as HUDManager;\n      if (hudManager) {\n        hudManager.update();\n      }\n    });\n\n    // Key events\n    mp.events.add('keydown', (keyCode: number) => {\n      this.handleKeyDown(keyCode);\n    });\n\n    mp.events.add('keyup', (keyCode: number) => {\n      this.handleKeyUp(keyCode);\n    });\n\n    // CEF events\n    mp.events.add('cef:emit', (eventName: string, data: any) => {\n      const uiManager = this.managers.get('ui') as UIManager;\n      if (uiManager) {\n        uiManager.handleCEFEvent(eventName, data);\n      }\n    });\n\n    // RPC call handler\n    mp.events.add('rpc:call', (eventName: string, callId: string, ...args: any[]) => {\n      this.handleRPCCall(eventName, callId, ...args);\n    });\n\n    Logger.success('✅ Global event handlers setup complete');\n  }\n\n  /**\n   * Handle key down events\n   */\n  private handleKeyDown(keyCode: number): void {\n    // F1 - Toggle chat\n    if (keyCode === 112) { // F1\n      const chatManager = this.managers.get('chat') as ChatManager;\n      if (chatManager) {\n        chatManager.toggle();\n      }\n    }\n\n    // F2 - Toggle HUD\n    if (keyCode === 113) { // F2\n      const hudManager = this.managers.get('hud') as HUDManager;\n      if (hudManager) {\n        hudManager.toggle();\n      }\n    }\n\n    // T - Open chat input\n    if (keyCode === 84) { // T\n      const chatManager = this.managers.get('chat') as ChatManager;\n      if (chatManager) {\n        chatManager.openInput();\n      }\n    }\n\n    // ESC - Close UI\n    if (keyCode === 27) { // ESC\n      const uiManager = this.managers.get('ui') as UIManager;\n      if (uiManager) {\n        uiManager.closeAll();\n      }\n    }\n  }\n\n  /**\n   * Handle key up events\n   */\n  private handleKeyUp(keyCode: number): void {\n    // Handle key up events if needed\n  }\n\n  /**\n   * Handle RPC calls from server\n   */\n  private async handleRPCCall(eventName: string, callId: string, ...args: any[]): Promise<void> {\n    try {\n      let result: any = null;\n      let error: string | undefined = undefined;\n\n      // Route RPC calls to appropriate managers\n      switch (eventName) {\n        case 'getPlayerPosition':\n          result = mp.players.local.position;\n          break;\n        \n        case 'getPlayerHealth':\n          result = mp.players.local.health;\n          break;\n        \n        case 'isPlayerInVehicle':\n          result = mp.players.local.vehicle !== null;\n          break;\n        \n        default:\n          error = `Unknown RPC call: ${eventName}`;\n          break;\n      }\n\n      // Send response back to server\n      mp.events.callRemote(`rpc:response:${callId}`, result, error);\n\n    } catch (err) {\n      mp.events.callRemote(`rpc:response:${callId}`, null, (err as Error).message);\n    }\n  }\n\n  /**\n   * Get manager instance\n   */\n  public getManager<T>(name: string): T | undefined {\n    return this.managers.get(name) as T;\n  }\n\n  /**\n   * Check if client is initialized\n   */\n  public get initialized(): boolean {\n    return this.isInitialized;\n  }\n\n  /**\n   * Shutdown the client gracefully\n   */\n  public async shutdown(): Promise<void> {\n    Logger.info('🛑 Shutting down client...');\n\n    // Shutdown managers\n    for (const [name, manager] of this.managers) {\n      try {\n        if (manager.shutdown) {\n          await manager.shutdown();\n          Logger.info(`  ✅ ${name} manager shutdown complete`);\n        }\n      } catch (error) {\n        Logger.error(`  ❌ Error shutting down ${name} manager:`, error);\n      }\n    }\n    \n    Logger.success('✅ Client shutdown complete');\n  }\n}\n\n// Global client instance\nlet clientInstance: RoleplayClient;\n\n// Initialize client when script loads\n(async () => {\n  try {\n    clientInstance = new RoleplayClient();\n    await clientInstance.initialize();\n  } catch (error) {\n    Logger.error('Failed to initialize client:', error);\n  }\n})();\n\n// Export client instance for other modules\nexport { clientInstance as Client };\n"], "names": ["EventManager", "static", "eventHandlers", "Map", "getInstance", "instance", "initialize", "console", "log", "this", "setupDefaultEvents", "mp", "events", "add", "message", "on", "eventName", "handler", "has", "set", "get", "push", "off", "handlers", "index", "indexOf", "splice", "remove", "for<PERSON>ach", "h", "delete", "callServer", "args", "callRemote", "callBrowser", "browser", "execute", "JSON", "stringify", "shutdown", "clear", "isVisible", "hud<PERSON><PERSON><PERSON>", "setupHUDElements", "setupHUDEvents", "game", "ui", "displayHud", "displayRadar", "toggle", "element", "toggleElement", "data", "updateData", "show", "call", "hide", "currentState", "visible", "update", "player", "players", "local", "hud<PERSON><PERSON>", "health", "getHealth", "armor", "getArmour", "position", "heading", "getHeading", "inVehicle", "vehicle", "speed", "getSpeed", "rpm", "getCurrentRpm", "gear", "getCurrentGear", "fuel", "engineOn", "getIsEngineRunning", "isElementVisible", "isAuthenticated", "player<PERSON><PERSON>", "setupAuthEvents", "response", "handleAuthResponse", "handleLogout", "success", "type", "authenticated", "isInputOpen", "setupChatEvents", "displayMessage", "sender", "openInput", "gui", "cursor", "closeInput", "sendMessage", "trim", "length", "inputOpen", "activeCharacter", "characters", "setupCharacterEvents", "character", "setActiveCharacter", "setChara<PERSON><PERSON>", "updates", "updateCharacter", "firstName", "lastName", "Object", "assign", "onPlayerDeath", "reason", "killer", "onPlayerChat", "text", "charactersList", "UIManager", "browsers", "activeBrowser", "setupUIEvents", "showUI", "hideUI", "updateUIData", "<PERSON><PERSON><PERSON><PERSON>", "name", "new", "handleCEFEvent", "warn", "closeAll", "destroy", "LogLevel", "<PERSON><PERSON>", "INFO", "setLogLevel", "level", "logLevel", "getTimestamp", "Date", "toISOString", "replace", "substring", "formatMessage", "map", "arg", "String", "join", "debug", "DEBUG", "formatted", "info", "WARN", "error", "ERROR", "SUCCESS", "__webpack_module_cache__", "__webpack_require__", "moduleId", "cachedModule", "undefined", "exports", "module", "__webpack_modules__", "RoleplayClient", "managers", "isInitialized", "constructor", "initializeManagers", "setupGlobalEvents", "managerClasses", "<PERSON>th<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "Chat<PERSON>anager", "HUDManager", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "keyCode", "handleKeyDown", "handleKeyUp", "uiManager", "callId", "handleRPCCall", "chat<PERSON>anager", "result", "err", "getManager", "initialized", "manager", "clientInstance"], "sourceRoot": ""}
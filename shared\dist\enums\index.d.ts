export declare enum PlayerState {
    OFFLINE = 0,
    ONLINE = 1,
    AWAY = 2,
    BUSY = 3,
    DEAD = 4,
    UNCONSCIOUS = 5
}
export declare enum Gender {
    MALE = "male",
    FEMALE = "female"
}
export declare enum VehicleState {
    PARKED = 0,
    DRIVING = 1,
    DAMAGED = 2,
    DESTROYED = 3,
    IMPOUNDED = 4
}
export declare enum JobType {
    UNEMPLOYED = "unemployed",
    POLICE = "police",
    MEDIC = "medic",
    MECHANIC = "mechanic",
    TAXI = "taxi",
    TRUCKER = "trucker",
    DELIVERY = "delivery",
    GARBAGE = "garbage",
    MINER = "miner",
    LUMBERJACK = "lumberjack",
    FISHERMAN = "fisherman",
    FARMER = "farmer",
    CHEF = "chef",
    BARTENDER = "bartender",
    SHOP_KEEPER = "shopkeeper",
    BANK_TELLER = "bankteller",
    LAWYER = "lawyer",
    JUDGE = "judge",
    REAL_ESTATE = "realestate",
    NEWS_REPORTER = "newsreporter"
}
export declare enum PropertyType {
    HOUSE = "house",
    APARTMENT = "apartment",
    BUSINESS = "business",
    GARAGE = "garage",
    WAREHOUSE = "warehouse"
}
export declare enum ItemType {
    WEAPON = "weapon",
    AMMO = "ammo",
    FOOD = "food",
    DRINK = "drink",
    MEDICAL = "medical",
    TOOL = "tool",
    CLOTHING = "clothing",
    ACCESSORY = "accessory",
    DOCUMENT = "document",
    KEY = "key",
    PHONE = "phone",
    ELECTRONICS = "electronics",
    DRUG = "drug",
    MISC = "misc"
}
export declare enum ChatType {
    LOCAL = "local",
    WHISPER = "whisper",
    SHOUT = "shout",
    ME = "me",
    DO = "do",
    OOC = "ooc",
    GLOBAL = "global",
    ADMIN = "admin",
    FACTION = "faction",
    JOB = "job",
    PHONE = "phone",
    RADIO = "radio"
}
export declare enum AdminLevel {
    PLAYER = 0,
    HELPER = 1,
    MODERATOR = 2,
    ADMIN = 3,
    SENIOR_ADMIN = 4,
    HEAD_ADMIN = 5,
    DEVELOPER = 6,
    OWNER = 7
}
export declare enum NotificationType {
    SUCCESS = "success",
    ERROR = "error",
    WARNING = "warning",
    INFO = "info"
}
export declare enum UIType {
    LOGIN = "login",
    REGISTER = "register",
    CHARACTER_CREATOR = "characterCreator",
    CHARACTER_SELECTOR = "characterSelector",
    INVENTORY = "inventory",
    PHONE = "phone",
    ATM = "atm",
    VEHICLE_HUD = "vehicleHud",
    SPEEDOMETER = "speedometer",
    CHAT = "chat",
    ADMIN_PANEL = "adminPanel",
    JOB_MENU = "jobMenu",
    SHOP = "shop",
    GARAGE = "garage",
    PROPERTY_MENU = "propertyMenu"
}
export declare enum InteractionType {
    DOOR = "door",
    VEHICLE = "vehicle",
    ATM = "atm",
    SHOP = "shop",
    JOB_POINT = "jobPoint",
    PROPERTY = "property",
    NPC = "npc",
    OBJECT = "object"
}
export declare enum WeatherType {
    CLEAR = "clear",
    CLOUDY = "cloudy",
    OVERCAST = "overcast",
    RAIN = "rain",
    THUNDER = "thunder",
    FOG = "fog",
    SNOW = "snow",
    BLIZZARD = "blizzard"
}
export declare enum TimePeriod {
    MORNING = "morning",
    AFTERNOON = "afternoon",
    EVENING = "evening",
    NIGHT = "night"
}
export declare enum FactionType {
    GANG = "gang",
    MAFIA = "mafia",
    MC = "mc",
    CARTEL = "cartel",
    GOVERNMENT = "government",
    BUSINESS = "business"
}
export declare enum VehicleClass {
    COMPACT = 0,
    SEDAN = 1,
    SUV = 2,
    COUPE = 3,
    MUSCLE = 4,
    SPORTS_CLASSIC = 5,
    SPORTS = 6,
    SUPER = 7,
    MOTORCYCLE = 8,
    OFF_ROAD = 9,
    INDUSTRIAL = 10,
    UTILITY = 11,
    VAN = 12,
    CYCLE = 13,
    BOAT = 14,
    HELICOPTER = 15,
    PLANE = 16,
    SERVICE = 17,
    EMERGENCY = 18,
    MILITARY = 19,
    COMMERCIAL = 20,
    TRAIN = 21
}
//# sourceMappingURL=index.d.ts.map
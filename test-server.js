/**
 * Test script to check if our server code works
 */

console.log('🧪 Testing server dependencies...');

try {
  // Test dotenv
  require('dotenv').config();
  console.log('✅ dotenv loaded');

  // Test mongoose
  const mongoose = require('mongoose');
  console.log('✅ mongoose loaded');

  // Test bcryptjs
  const bcrypt = require('bcryptjs');
  console.log('✅ bcryptjs loaded');

  // Test jsonwebtoken
  const jwt = require('jsonwebtoken');
  console.log('✅ jsonwebtoken loaded');

  console.log('🎉 All dependencies loaded successfully!');
  
  // Test our compiled code
  console.log('🧪 Testing compiled server code...');
  
  // Mock RageMP global objects for testing
  global.mp = {
    events: {
      add: (name, handler) => console.log(`📡 Event registered: ${name}`),
      remove: (name, handler) => console.log(`📡 Event removed: ${name}`)
    },
    players: {
      call: (event, ...args) => console.log(`📞 Players call: ${event}`)
    }
  };
  
  // Try to load our server code
  require('./packages/roleplay/dist/index.js');
  
} catch (error) {
  console.error('❌ Error:', error.message);
  console.error('Stack:', error.stack);
}

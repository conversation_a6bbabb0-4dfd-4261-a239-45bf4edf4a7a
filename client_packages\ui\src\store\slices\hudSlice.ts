import { createSlice, PayloadAction } from '@reduxjs/toolkit'

interface HUDState {
  isVisible: boolean
  showHealthBar: boolean
  showArmorBar: boolean
  showStaminaBar: boolean
  showHungerBar: boolean
  showThirstBar: boolean
  showMoney: boolean
  showTime: boolean
  showLocation: boolean
  showSpeedometer: boolean
  showMinimap: boolean
  position: {
    x: number
    y: number
  }
  scale: number
  opacity: number
  layout: 'default' | 'minimal' | 'compact'
}

const initialState: HUDState = {
  isVisible: true,
  showHealthBar: true,
  showArmorBar: true,
  showStaminaBar: true,
  showHungerBar: true,
  showThirstBar: true,
  showMoney: true,
  showTime: true,
  showLocation: true,
  showSpeedometer: true,
  showMinimap: true,
  position: {
    x: 20,
    y: 20,
  },
  scale: 1.0,
  opacity: 0.9,
  layout: 'default',
}

const hudSlice = createSlice({
  name: 'hud',
  initialState,
  reducers: {
    toggleVisibility: (state) => {
      state.isVisible = !state.isVisible
    },
    setVisibility: (state, action: PayloadAction<boolean>) => {
      state.isVisible = action.payload
    },
    toggleElement: (state, action: PayloadAction<keyof Omit<HUDState, 'isVisible' | 'position' | 'scale' | 'opacity' | 'layout'>>) => {
      state[action.payload] = !state[action.payload]
    },
    setElementVisibility: (state, action: PayloadAction<{
      element: keyof Omit<HUDState, 'isVisible' | 'position' | 'scale' | 'opacity' | 'layout'>
      visible: boolean
    }>) => {
      state[action.payload.element] = action.payload.visible
    },
    setPosition: (state, action: PayloadAction<{ x: number; y: number }>) => {
      state.position = action.payload
    },
    setScale: (state, action: PayloadAction<number>) => {
      state.scale = Math.max(0.5, Math.min(2.0, action.payload))
    },
    setOpacity: (state, action: PayloadAction<number>) => {
      state.opacity = Math.max(0.1, Math.min(1.0, action.payload))
    },
    setLayout: (state, action: PayloadAction<'default' | 'minimal' | 'compact'>) => {
      state.layout = action.payload
    },
    resetToDefaults: (state) => {
      Object.assign(state, initialState)
    },
  },
})

export const {
  toggleVisibility,
  setVisibility,
  toggleElement,
  setElementVisibility,
  setPosition,
  setScale,
  setOpacity,
  setLayout,
  resetToDefaults,
} = hudSlice.actions

export default hudSlice.reducer

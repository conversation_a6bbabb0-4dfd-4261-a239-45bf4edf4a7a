{"name": "@ragemp-rp/ui", "version": "1.0.0", "description": "RageMP Roleplay React UI", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "preview": "vite preview", "clean": "<PERSON><PERSON><PERSON> dist"}, "dependencies": {"react": "^18.2.0", "react-dom": "^18.2.0", "@reduxjs/toolkit": "^2.0.1", "react-redux": "^9.0.4", "@nextui-org/react": "^2.2.9", "framer-motion": "^10.16.16", "lucide-react": "^0.294.0", "react-hook-form": "^7.48.2", "@hookform/resolvers": "^3.3.2", "zod": "^3.22.4", "clsx": "^2.0.0", "tailwind-merge": "^2.2.0"}, "devDependencies": {"@types/react": "^18.2.43", "@types/react-dom": "^18.2.17", "@vitejs/plugin-react": "^4.2.1", "typescript": "^5.3.3", "vite": "^5.0.8", "tailwindcss": "^3.3.6", "autoprefixer": "^10.4.16", "postcss": "^8.4.32", "rimraf": "^5.0.5"}}
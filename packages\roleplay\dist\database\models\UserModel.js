"use strict";
/**
 * User Model for MongoDB
 */
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.UserModel = void 0;
const mongoose_1 = __importStar(require("mongoose"));
const shared_1 = require("@ragemp-rp/shared");
const UserSchema = new mongoose_1.Schema({
    username: {
        type: String,
        required: true,
        unique: true,
        lowercase: true,
        trim: true,
        minlength: 3,
        maxlength: 20,
        match: /^[a-zA-Z0-9_]+$/
    },
    email: {
        type: String,
        required: true,
        unique: true,
        lowercase: true,
        trim: true,
        match: /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    },
    password: {
        type: String,
        required: true,
        select: false // Don't include password in queries by default
    },
    socialClub: {
        type: String,
        required: true,
        trim: true
    },
    adminLevel: {
        type: Number,
        enum: Object.values(shared_1.AdminLevel),
        default: shared_1.AdminLevel.PLAYER
    },
    registrationDate: {
        type: Date,
        default: Date.now
    },
    lastLogin: {
        type: Date,
        default: Date.now
    },
    registrationIP: {
        type: String,
        required: true
    },
    lastIP: {
        type: String,
        required: true
    },
    banned: {
        type: Boolean,
        default: false
    },
    banReason: {
        type: String,
        default: null
    },
    banExpiry: {
        type: Date,
        default: null
    },
    bannedBy: {
        type: String,
        default: null
    },
    playtime: {
        type: Number,
        default: 0 // in minutes
    },
    money: {
        type: Number,
        default: 5000 // Starting money
    },
    bankMoney: {
        type: Number,
        default: 0
    },
    settings: {
        language: {
            type: String,
            default: 'en'
        },
        notifications: {
            type: Boolean,
            default: true
        },
        chatTimestamps: {
            type: Boolean,
            default: true
        },
        uiScale: {
            type: Number,
            default: 1.0,
            min: 0.5,
            max: 2.0
        }
    },
    statistics: {
        totalLogins: {
            type: Number,
            default: 0
        },
        charactersCreated: {
            type: Number,
            default: 0
        },
        lastCharacterId: {
            type: mongoose_1.Schema.Types.ObjectId,
            ref: 'Character',
            default: null
        }
    }
}, {
    timestamps: true,
    collection: 'users'
});
// Indexes for better performance
UserSchema.index({ username: 1 });
UserSchema.index({ email: 1 });
UserSchema.index({ socialClub: 1 });
UserSchema.index({ banned: 1 });
UserSchema.index({ adminLevel: 1 });
// Pre-save middleware to increment login count
UserSchema.pre('save', function (next) {
    if (this.isModified('lastLogin')) {
        this.statistics.totalLogins += 1;
    }
    next();
});
// Instance methods
UserSchema.methods.isBanned = function () {
    if (!this.banned)
        return false;
    if (!this.banExpiry)
        return true; // Permanent ban
    return new Date() < this.banExpiry;
};
UserSchema.methods.isAdmin = function () {
    return this.adminLevel > shared_1.AdminLevel.PLAYER;
};
UserSchema.methods.canUseAdminCommand = function (requiredLevel) {
    return this.adminLevel >= requiredLevel;
};
UserSchema.methods.addPlaytime = function (minutes) {
    this.playtime += minutes;
};
UserSchema.methods.getFormattedPlaytime = function () {
    const hours = Math.floor(this.playtime / 60);
    const minutes = this.playtime % 60;
    return `${hours}h ${minutes}m`;
};
// Static methods
UserSchema.statics.findByUsername = function (username) {
    return this.findOne({ username: username.toLowerCase() });
};
UserSchema.statics.findByEmail = function (email) {
    return this.findOne({ email: email.toLowerCase() });
};
UserSchema.statics.findBySocialClub = function (socialClub) {
    return this.findOne({ socialClub: socialClub });
};
UserSchema.statics.getBannedUsers = function () {
    return this.find({ banned: true });
};
UserSchema.statics.getAdmins = function () {
    return this.find({ adminLevel: { $gt: shared_1.AdminLevel.PLAYER } });
};
exports.UserModel = mongoose_1.default.model('User', UserSchema);
//# sourceMappingURL=UserModel.js.map
/**
 * Authentication Module
 * Handles player login, registration, and session management
 */

import bcrypt from 'bcryptjs';
import jwt from 'jsonwebtoken';
import { EventManager } from '../../events/EventManager';
import { Logger } from '../../utils/Logger';
import { UserModel } from '../../database/models/UserModel';
import { 
  ClientToServerEvents, 
  ServerToClientEvents,
  ILoginData,
  IRegisterData,
  IAuthResponse 
} from '@ragemp-rp/shared';

export class AuthModule {
  private eventManager: EventManager;
  private jwtSecret: string;
  private authenticatedPlayers: Map<number, any> = new Map();

  constructor() {
    this.eventManager = EventManager.getInstance();
    this.jwtSecret = process.env.JWT_SECRET || 'your-secret-key-change-this';
  }

  /**
   * Initialize the authentication module
   */
  public async initialize(): Promise<void> {
    Logger.info('Initializing Authentication Module...');
    
    this.setupEventHandlers();
    
    Logger.success('Authentication Module initialized successfully');
  }

  /**
   * Setup event handlers
   */
  private setupEventHandlers(): void {
    // Login event
    this.eventManager.on(ClientToServerEvents.AUTH_LOGIN, this.handleLogin.bind(this));
    
    // Register event
    this.eventManager.on(ClientToServerEvents.AUTH_REGISTER, this.handleRegister.bind(this));
    
    // Logout event
    this.eventManager.on(ClientToServerEvents.AUTH_LOGOUT, this.handleLogout.bind(this));
  }

  /**
   * Handle player join
   */
  public onPlayerJoin(player: PlayerMp): void {
    Logger.info(`Player ${player.name} joined - showing login screen`);
    
    // Show login UI
    this.eventManager.emitClient(player, ServerToClientEvents.SHOW_UI, {
      type: 'login',
      data: {
        serverName: 'RageMP Roleplay Server',
        version: '1.0.0'
      }
    });
  }

  /**
   * Handle login request
   */
  private async handleLogin(player: PlayerMp, data: ILoginData): Promise<void> {
    try {
      Logger.info(`Login attempt from ${player.name}: ${data.username}`);

      // Validate input
      if (!data.username || !data.password) {
        this.sendAuthResponse(player, {
          success: false,
          message: 'Username and password are required'
        });
        return;
      }

      // Find user in database
      const user = await UserModel.findOne({ 
        username: data.username.toLowerCase() 
      }).select('+password');

      if (!user) {
        this.sendAuthResponse(player, {
          success: false,
          message: 'Invalid username or password'
        });
        return;
      }

      // Verify password
      const isValidPassword = await bcrypt.compare(data.password, user.password);
      if (!isValidPassword) {
        this.sendAuthResponse(player, {
          success: false,
          message: 'Invalid username or password'
        });
        return;
      }

      // Check if user is already online
      if (this.isUserOnline((user as any)._id.toString())) {
        this.sendAuthResponse(player, {
          success: false,
          message: 'This account is already logged in'
        });
        return;
      }

      // Generate JWT token
      const token = this.generateToken((user as any)._id.toString(), user.username);

      // Update user's last login
      user.lastLogin = new Date();
      user.lastIP = player.ip;
      await user.save();

      // Store authenticated player
      this.authenticatedPlayers.set(player.id, {
        userId: (user as any)._id.toString(),
        username: user.username,
        token: token,
        loginTime: new Date()
      });

      // Send success response
      this.sendAuthResponse(player, {
        success: true,
        message: 'Login successful',
        token: token,
        player: {
          id: player.id,
          name: player.name,
          socialClub: player.socialClub,
          ip: player.ip,
          ping: player.ping,
          dimension: player.dimension,
          position: player.position,
          heading: player.heading,
          health: player.health,
          armor: player.armour,
          weapon: player.weapon,
          ammo: player.getWeaponAmmo(player.weapon)
        }
      });

      Logger.success(`Player ${player.name} logged in successfully as ${user.username}`);

    } catch (error) {
      Logger.error('Login error:', error);
      this.sendAuthResponse(player, {
        success: false,
        message: 'An error occurred during login'
      });
    }
  }

  /**
   * Handle registration request
   */
  private async handleRegister(player: PlayerMp, data: IRegisterData): Promise<void> {
    try {
      Logger.info(`Registration attempt from ${player.name}: ${data.username}`);

      // Validate input
      const validation = this.validateRegistrationData(data);
      if (!validation.valid) {
        this.sendAuthResponse(player, {
          success: false,
          message: validation.message
        });
        return;
      }

      // Check if username already exists
      const existingUser = await UserModel.findOne({ 
        username: data.username.toLowerCase() 
      });

      if (existingUser) {
        this.sendAuthResponse(player, {
          success: false,
          message: 'Username already exists'
        });
        return;
      }

      // Check if email already exists
      const existingEmail = await UserModel.findOne({ 
        email: data.email.toLowerCase() 
      });

      if (existingEmail) {
        this.sendAuthResponse(player, {
          success: false,
          message: 'Email already registered'
        });
        return;
      }

      // Hash password
      const hashedPassword = await bcrypt.hash(data.password, 12);

      // Create new user
      const newUser = new UserModel({
        username: data.username.toLowerCase(),
        email: data.email.toLowerCase(),
        password: hashedPassword,
        socialClub: player.socialClub,
        registrationIP: player.ip,
        lastIP: player.ip,
        registrationDate: new Date(),
        lastLogin: new Date()
      });

      await newUser.save();

      Logger.success(`New user registered: ${data.username}`);

      this.sendAuthResponse(player, {
        success: true,
        message: 'Registration successful! You can now login.'
      });

    } catch (error) {
      Logger.error('Registration error:', error);
      this.sendAuthResponse(player, {
        success: false,
        message: 'An error occurred during registration'
      });
    }
  }

  /**
   * Handle logout request
   */
  private handleLogout(player: PlayerMp): void {
    try {
      const authData = this.authenticatedPlayers.get(player.id);
      if (authData) {
        this.authenticatedPlayers.delete(player.id);
        Logger.info(`Player ${player.name} (${authData.username}) logged out`);
      }

      this.eventManager.emitClient(player, ServerToClientEvents.AUTH_LOGOUT);
      
      // Show login screen again
      this.onPlayerJoin(player);

    } catch (error) {
      Logger.error('Logout error:', error);
    }
  }

  /**
   * Validate registration data
   */
  private validateRegistrationData(data: IRegisterData): { valid: boolean; message: string } {
    if (!data.username || data.username.length < 3 || data.username.length > 20) {
      return { valid: false, message: 'Username must be between 3 and 20 characters' };
    }

    if (!/^[a-zA-Z0-9_]+$/.test(data.username)) {
      return { valid: false, message: 'Username can only contain letters, numbers, and underscores' };
    }

    if (!data.email || !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(data.email)) {
      return { valid: false, message: 'Please enter a valid email address' };
    }

    if (!data.password || data.password.length < 6) {
      return { valid: false, message: 'Password must be at least 6 characters long' };
    }

    if (data.password !== data.confirmPassword) {
      return { valid: false, message: 'Passwords do not match' };
    }

    return { valid: true, message: '' };
  }

  /**
   * Generate JWT token
   */
  private generateToken(userId: string, username: string): string {
    return jwt.sign(
      { userId, username },
      this.jwtSecret,
      { expiresIn: '24h' }
    );
  }

  /**
   * Verify JWT token
   */
  private verifyToken(token: string): any {
    try {
      return jwt.verify(token, this.jwtSecret);
    } catch (error) {
      return null;
    }
  }

  /**
   * Check if user is online
   */
  private isUserOnline(userId: string): boolean {
    for (const [playerId, authData] of this.authenticatedPlayers) {
      if (authData.userId === userId) {
        return true;
      }
    }
    return false;
  }

  /**
   * Send authentication response to client
   */
  private sendAuthResponse(player: PlayerMp, response: IAuthResponse): void {
    this.eventManager.emitClient(player, ServerToClientEvents.AUTH_RESPONSE, response);
  }

  /**
   * Get authenticated player data
   */
  public getAuthenticatedPlayer(player: PlayerMp): any {
    return this.authenticatedPlayers.get(player.id);
  }

  /**
   * Check if player is authenticated
   */
  public isPlayerAuthenticated(player: PlayerMp): boolean {
    return this.authenticatedPlayers.has(player.id);
  }

  /**
   * Shutdown module
   */
  public async shutdown(): Promise<void> {
    Logger.info('Shutting down Authentication Module...');
    this.authenticatedPlayers.clear();
  }
}
